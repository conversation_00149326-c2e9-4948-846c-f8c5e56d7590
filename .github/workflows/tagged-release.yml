name: Tagged Release
on:
  push:
    tags:
      - '*'
jobs:
  tagged-release:
    name: "Tagged Release"
    runs-on: "ubuntu-latest"
  
    steps:
      - id: checkout
        name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - id: zip_secret_service
        name: Zip SecretService
        uses: vimtor/action-zip@v1
        with:
          files: Plugins/SecretService/Release/secret-service.artifactbundle
          dest: SecretService.zip
      - id: zip_og_regex
        name: Zip OGRegex
        uses: vimtor/action-zip@v1
        with:
          files: Tools/OGRegex/Release
          dest: OGRegex.zip
      - id: zip_og_xcode_templates
        name: Zip XcodeTemplates
        uses: vimtor/action-zip@v1
        with:
          files: XcodeTemplates
          dest: XcodeTemplates.zip
      - id: zip_og_asset_fetcher
        name: Zip OGAssetFetcher
        uses: vimtor/action-zip@v1
        with:
          files: Tools/OGAssetsFetcher/Release
          dest: OGAssetsFetcher.zip
      - id: release
        name: Release
        uses: "marvinpinto/action-automatic-releases@v1.2.1"
        with:
          repo_token: "${{ secrets.GITHUB_TOKEN }}"
          prerelease: false
          files: |
            SecretService.zip
            OGRegex.zip
            XcodeTemplates.zip
            OGAssetsFetcher.zip
