name: CI

on:  
  push:
  pull_request:
    types: [opened, reopened]
    branches:
      - main
  workflow_dispatch:
    inputs:
      trigger:
        description: 'dispatch trigger'
        required: false
concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true

jobs:
  ci: # unique id
    timeout-minutes: 180
    name: CI build pipeline (OGKit)
    runs-on: macos-15-large
    if: startsWith(github.ref, 'refs/heads/') == true
    steps:
      - id: xcode-setup
        name: Setup Xcode version
        uses: maxim-lobanov/setup-xcode@v1.6.0
        with:
          xcode-version: '16.4'
      - name: Apply netrc creds with a JSON block
        uses: bcomnes/netrc-creds@v3
        with:
          creds: |
            [
              {
                "machine": "github.com",
                "login": "og-dx-aac-ci",
                "password": "${{ secrets.CI_GITHUB_TOKEN }}"
              },
              {
                "machine": "maven.pkg.github.com",
                "login": "${{ secrets.GH_PACKAGES_USERNAME }}",
                "password": "${{ secrets.GH_PACKAGES_TOKEN }}"
              }
            ]
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Setting fetch_depth to 0 provides the whole project history and enables comparison between latest tag ad head used for versioning and commit history
      - id: ruby-setup
        name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.2.2
          bundler-cache: true
      - uses: actions/cache@v3
        with:
          path: packages_cache
          key: ${{ runner.os }}-packages-${{ hashFiles('**/Package.resolved') }}
          restore-keys: |
            ${{ runner.os }}-packages-
      - name: Run Package Tests
        run: |
          make test
        env:
          SLACK_URL: ${{ secrets.SLACK_HOOK_URL }}
          TERM: xterm-256color
          HOMEBREW_NO_INSTALL_CLEANUP: TRUE
          FASTLANE_DONT_STORE_PASSWORD: 1
          FASTLANE_HIDE_CHANGELOG: true
          FASTLANE_SKIP_UPDATE_CHECK: true
          FASTLANE_XCODE_LIST_TIMEOUT: 60
          FASTLANE_XCODEBUILD_SETTINGS_TIMEOUT: 60
