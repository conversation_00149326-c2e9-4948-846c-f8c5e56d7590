name: Deploy documentation
run-name: ${{ github.actor }} is deploying the OGAppKit Documentation
on:
    push:
      branches: [main, OGDocsGenerator_gh_action]

jobs:
    deploy:
        name: Deploy to GitHub Pages
        runs-on: macos-13-large
        steps:
            - uses: actions/checkout@v3

            - uses: actions/setup-node@v3
              with:
                node-version: 18.x

            - name: Setup Ruby
              uses: ruby/setup-ruby@v1
              with:
                ruby-version: 3.2.2
                bundler-cache: true          
              
            - name: Generate documentation
              run: make run_og-docs-generator

            - name: Build website
              working-directory: docs
              run: | 
                npm install
                npm run build

            - name: Deploy to GitHub Pages
              uses: peaceiris/actions-gh-pages@v3
              with:
                github_token: ${{ secrets.GITHUB_TOKEN }}
                # Build output to publish to the `gh-pages` branch:
                publish_dir: ./docs/build
