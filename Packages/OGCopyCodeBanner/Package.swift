// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
  name: "OGCopyCodeBanner",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    // Products define the executables and libraries a package produces, making them visible to other packages.
    .library(
      name: "OGCopyCodeBanner",
      targets: ["OGCopyCodeBanner"]
    ),
    .library(
      name: "OGCopyCodeBannerTestsUtils",
      targets: ["OGCopyCodeBannerTestsUtils"]
    )
  ],
  dependencies: [
    .package(
      path: "../OGCore"
    ),
    .package(
      path: "../OGFeatureKit"
    ),
    .package(
      path: "../OGExternalDependencies/OGDIService"
    ),
    .package(
      path: "../OGDialogCoordinator"
    ),
    .package(
      path: "../OGDialogCoordinator/Packages/OGScreenViewUpdate"
    )
  ],
  targets: [
    // Targets are the basic building blocks of a package, defining a module or a test suite.
    // Targets can depend on other targets in this package and products from dependencies.
    .target(
      name: "OGCopyCodeBanner",
      dependencies: [
        "OGCore",
        "OGDialogCoordinator",
        "OGDIService",
        "OGFeatureKit",
        "OGScreenViewUpdate"
      ]
    ),
    .target(
      name: "OGCopyCodeBannerTestsUtils",
      dependencies: [
        "OGCopyCodeBanner"
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "OGCopyCodeBannerTests",
      dependencies: ["OGCopyCodeBanner", "OGCopyCodeBannerTestsUtils"]
    )
  ]
)
