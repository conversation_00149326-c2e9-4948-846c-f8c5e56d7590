import Combine
import OGCopyCodeBanner
import OGCore
import OGDIService
import OGFeatureKit
import OGIdentifier

// MARK: - CopyCodeBannerFeatureAdapterMock

public final class CopyCodeBannerFeatureAdapterMock: CopyCodeBannerFeatureAdaptable {
  public var configuration: CurrentValueSubject<CopyCodeBannerFeatureConfigurable, Never>

  public var isAdaptedFeatureEnabled = true

  public init(isEnabled: Bool = true, supportedUrls: [String] = []) {
    var featureConfiguration = CopyCodeBannerFeatureConfiguration()
    featureConfiguration.isEnabled = isEnabled
    featureConfiguration.supportedUrls = supportedUrls
    self.configuration = CurrentValueSubject(featureConfiguration)
  }
}

// MARK: - CopyCodeBannerFeatureAdapterContainer + AutoRegistering

extension CopyCodeBannerFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
    guard OGCoreContainer.shared.appEnvironment().isTestsBuild else { return }
    copyCodeBanner.register {
      CopyCodeBannerFeatureAdapter(configuration: CopyCodeBannerFeatureConfiguration())
    }
  }
}
