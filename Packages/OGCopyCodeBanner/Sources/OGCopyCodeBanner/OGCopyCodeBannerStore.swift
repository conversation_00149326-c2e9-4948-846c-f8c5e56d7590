import Combine
import OGCore
import OGDIService
import OGDomainStore
import OGScreenViewUpdate

public typealias OGCopyCodeBannerStore = OGDomainStore<OGCopyCodeBannerState, OGCopyCodeBannerAction>

extension OGDomainStore where State == OGCopyCodeBannerState, Action == OGCopyCodeBannerAction {
  public static func make() -> OGCopyCodeBannerStore {
    OGCopyCodeBannerStore(
      reducer: OGCopyCodeBannerState.Reducer.reduce,
      connector: OGCopyCodeBannerState.Connector()
    )
  }
}

// MARK: - OGCopyCodeBannerAction

public enum OGCopyCodeBannerAction: OGDomainAction, Equatable {
  case setDealSeen(Bool)
  /// Private
  case _setCurrentUrlMatched(Bool)
}

// MARK: - OGCopyCodeBannerState

public struct OGCopyCodeBannerState: OGDomainState, Equatable, Sendable {
  public private(set) var isAwaitingUpdate: Bool
  private(set) var isCurrentUrlMatched: Bool
  private(set) var isDealSeen: Bool

  public var shouldShowBanner: Bool {
    isCurrentUrlMatched && isDealSeen
  }

  public init(
    isAwaitingUpdate: Bool = false,
    isCurrentUrlMatched: Bool = false,
    isDealSeen: Bool = false
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate
    self.isCurrentUrlMatched = isCurrentUrlMatched
    self.isDealSeen = isDealSeen
  }

  mutating func update(
    isCurrentUrlMatched: Bool? = nil,
    isDealSeen: Bool? = nil
  ) {
    self.isCurrentUrlMatched = isCurrentUrlMatched ?? self.isCurrentUrlMatched
    self.isDealSeen = isDealSeen ?? self.isDealSeen
  }

  public static let initial: Self = .init()
}

// MARK: OGCopyCodeBannerState.Reducer

extension OGCopyCodeBannerState {
  enum Reducer: OGDomainActionReducible {
    static func reduce(
      _ state: inout OGCopyCodeBannerState,
      with action: OGCopyCodeBannerAction
    ) {
      switch action {
      case let .setDealSeen(isDealSeen):
        state.update(isDealSeen: isDealSeen)

      case let ._setCurrentUrlMatched(isCurrentUrlMatched):
        state.update(isCurrentUrlMatched: isCurrentUrlMatched)
      }
    }
  }
}

// MARK: OGCopyCodeBannerState.Connector

extension OGCopyCodeBannerState {
  actor Connector: OGDomainConnector {
    private let copyCodeBannerFeature: CopyCodeBannerFeatureAdaptable
    private let screenViewUpdate: OGScreenViewUpdatable
    private var cancellables = Set<AnyCancellable>()

    init(
      copyCodeBannerFeature: CopyCodeBannerFeatureAdaptable = CopyCodeBannerFeatureAdapterContainer.shared.copyCodeBanner(),
      screenViewUpdate: OGScreenViewUpdatable = OGScreenViewUpdateContainer.shared.screenViewUpdate()
    ) {
      self.copyCodeBannerFeature = copyCodeBannerFeature
      self.screenViewUpdate = screenViewUpdate
    }

    func configure(
      dispatch: @escaping (OGCopyCodeBannerAction) async -> Void
    ) async {
      Publishers.CombineLatest(
        copyCodeBannerFeature.configuration,
        screenViewUpdate.update
      )
      .removeDuplicates { $0.1 == $1.1 }
      .filter(\.0.isEnabled)
      .map { config, url -> Bool in
        config.supportedUrls.first { url?.isMatched(by: $0) ?? false } != nil
      }
      .sink { isURLMatched in
        Task {
          await dispatch(._setCurrentUrlMatched(isURLMatched))
        }
      }
      .store(in: &cancellables)
    }
  }
}
