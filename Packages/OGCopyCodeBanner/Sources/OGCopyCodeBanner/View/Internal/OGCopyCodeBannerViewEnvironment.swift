// swiftlint:disable type_name

import SwiftUI

// MARK: - OGCopyCodeBannerViewStyleKey

struct OGCopyCodeBannerViewStyleKey: EnvironmentKey {
  static var defaultValue = AnyOGCopyCodeBannerViewStyle(style: DefaultOGCopyCodeBannerViewStyle())
}

extension EnvironmentValues {
  var styleOGCopyCodeBannerView: AnyOGCopyCodeBannerViewStyle {
    get { self[OGCopyCodeBannerViewStyleKey.self] }
    set { self[OGCopyCodeBannerViewStyleKey.self] = newValue }
  }
}
