// swiftlint:disable type_name

import SwiftUI

/// The properties of a OGCopyCodeBannerView.
public struct OGCopyCodeBannerViewStyleConfiguration {
  /// A view that describes the content of a the OGCopyCodeBannerView.
  public let content: OGCopyCodeBannerViewStyleConfiguration.Content

  /// The type-erased content of a OGCopyCodeBannerView.
  public struct Content: View {
    init(content: some View) {
      self.body = AnyView(content)
    }

    /// The type of view representing the body of this view.
    public private(set) var body: AnyView
  }
}
