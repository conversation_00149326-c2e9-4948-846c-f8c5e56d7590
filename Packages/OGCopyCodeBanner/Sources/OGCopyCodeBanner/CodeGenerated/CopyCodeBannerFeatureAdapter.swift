import Combine
import Foundation
import OGCore
import OGDIService
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import OGMacros

// MARK: - CopyCodeBannerFeatureConfigurable

public protocol CopyCodeBannerFeatureConfigurable {
  var isEnabled: Bool { get set }
  var supportedUrls: [String] { get set }
}

// MARK: - CopyCodeBannerFeatureAdapter

public final class CopyCodeBannerFeatureAdapter: OGFeatureAdapter, CopyCodeBannerFeatureAdaptable {
  
  override public class var featureName: OGFeature.Name { OGIdentifier.copyCodeBanner.value }
  
  public let configuration: CurrentValueSubject<CopyCodeBannerFeatureConfigurable, Never>
  private var subscriptions = Set<AnyCancellable>()
  
  public init(configuration: CopyCodeBannerFeatureConfigurable?) {
    guard let configuration else {
      fatalError("The CopyCodeBannerConfiguration has not been registered")
    }
    self.configuration = CurrentValueSubject(configuration)
    super.init()
    
    receiveUpdates()
  }
  
  private func receiveUpdates() {
    $feature.sink { [weak self] feature in
      guard let self else { return }
      var updatedConfiguration = self.configuration.value
      guard let feature else {
        updatedConfiguration.isEnabled = false
        self.configuration.send(updatedConfiguration)
        return
      }
      updatedConfiguration.isEnabled = feature.isEnabled
      
      let supportedUrls: [String] = feature.customValue(for: OGFeatureKey.CustomValues.CopyCodeBanner.supportedUrls)
      updatedConfiguration.supportedUrls = supportedUrls
      
      self.configuration.send(updatedConfiguration)
    }.store(in: &subscriptions)
  }
}

// MARK: - CopyCodeBannerFeatureAdaptable

public protocol CopyCodeBannerFeatureAdaptable: OGFeatureAdaptable {
  var configuration: CurrentValueSubject<CopyCodeBannerFeatureConfigurable, Never> { get }
}

// MARK: - OGFeatureKey.CustomValues.CopyCodeBanner

extension OGFeatureKey.CustomValues {
  public enum CopyCodeBanner: String, OGKeyReceivable {
    public var value: String {
      rawValue
    }
    
    case supportedUrls
  }
}

extension OGIdentifier {
  public static let copyCodeBanner = #identifier("copyCodeBanner")
}
