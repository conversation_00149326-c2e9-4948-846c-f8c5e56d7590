import XCTest

import Foundation
@testable import OGAirshipKit

final class OGAirshipInboxButtonViewStoreReducerTests: XCTestCase {
  func test_GIVEN_initialState_WHEN_updated_THEN_stateUpdated() throws {
    var state = OGAirshipInboxButton.ViewState.initial
    OGAirshipInboxButton.Reducer.reduce(
      &state,
      with: ._updated(unreadCount: 1)
    )
    XCTAssertEqual(
      OGAirshipInboxButton.ViewState(unreadCount: 1),
      state
    )
  }

  func test_GIVEN_initialState_WHEN_enabled_THEN_stateUpdated() throws {
    var state = OGAirshipInboxButton.ViewState.initial
    OGAirshipInboxButton.Reducer.reduce(
      &state,
      with: ._enabled(true)
    )
    XCTAssertEqual(
      OGAirshipInboxButton.ViewState(isEnabled: true),
      state
    )
  }

  func test_GIVEN_initialState_WHEN_didTap_THEN_state_NOT_updated() throws {
    var state = OGAirshipInboxButton.ViewState.initial
    OGAirshipInboxButton.Reducer.reduce(
      &state,
      with: .didTap
    )
    XCTAssertEqual(
      OGAirshipInboxButton.ViewState.initial,
      state
    )
  }
}
