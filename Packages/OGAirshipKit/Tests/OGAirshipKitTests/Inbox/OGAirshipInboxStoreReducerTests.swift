import OGAirshipKitTestsUtils
import OGIdentifierTestsUtils
import XCTest

@testable import OGAirshipKit

final class OGAirshipInboxStoreReducerTests: XCTestCase {
  func test_WHEN_deleteMessage_THEN_isAwaitingForUpdate() throws {
    var state = OGAirshipInboxState.initial
    OGAirshipInboxState.Reducer.reduce(
      &state,
      with: .deleteMessage(with: .stub)
    )
    XCTAssertEqual(
      OGAirshipInboxState(isAwaitingUpdate: true),
      state
    )
  }

  func test_WHEN_markRead_THEN_isAwaitingForUpdate() throws {
    var state = OGAirshipInboxState.initial
    OGAirshipInboxState.Reducer.reduce(
      &state,
      with: .markRead(messageId: .stub)
    )
    XCTAssertEqual(
      OGAirshipInboxState(isAwaitingUpdate: true),
      state
    )
  }

  func test_WHEN_refreshMessages_THEN_isAwaitingForUpdate() throws {
    var state = OGAirshipInboxState.initial
    OGAirshipInboxState.Reducer.reduce(
      &state,
      with: .refreshMessages
    )
    XCTAssertEqual(
      OGAirshipInboxState(isAwaitingUpdate: true),
      state
    )
  }

  func test_WHEN_setMessagesAndUnreadCount_THEN_stateUpdated() throws {
    var state = OGAirshipInboxState.initial
    OGAirshipInboxState.Reducer.reduce(
      &state,
      with: ._set(messages: .stub, unreadCount: 1)
    )
    XCTAssertEqual(
      OGAirshipInboxState(isAwaitingUpdate: false, messages: .stub, unreadCount: 1),
      state
    )
  }
}
