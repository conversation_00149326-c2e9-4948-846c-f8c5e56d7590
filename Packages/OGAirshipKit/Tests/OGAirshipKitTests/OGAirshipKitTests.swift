import AirshipCore
import Combine
@testable import OGAirshipKit
import OGAirshipKitTestsUtils
import XCTest

final class OGAirshipKitTests: XCTestCase {
  @MainActor
  func test_WHEN_init_THEN_takeOff() async throws {
    let expectedIsInProduction = true
    let expectedKey = String.stub
    let expectedSecret = String.stub
    let expectedurlAllowListScopeOpenURL = ["*"]
    let expectedLaunchOptions = [UIApplication.LaunchOptionsKey.remoteNotification: String.stub]
    let airshipSecretMock = OGAirshipSecretMock()
    airshipSecretMock.mock.key.getter.mockCall { _ in
      .stub
    }

    airshipSecretMock.mock.secret.getter.mockCall { _ in
      .stub
    }

    let airshipFeatureAdapterMock = OGAirshipFeatureAdapterMock()
    airshipFeatureAdapterMock.mock.configuration.getter.mockCall { _ in
      CurrentValueSubject(AirshipFeatureConfig.stub)
    }

    var isInProduction: Bool?
    var key: String?
    var secret: String?
    var urlAllowListScopeOpenURL: [String]?
    var launchOptions: [UIApplication.LaunchOptionsKey: Any]?

    let expectation = expectation(description: "takeOff")

    let takeOff: (AirshipConfig?, [UIApplication.LaunchOptionsKey: Any]?) -> Void = { config, launch in
      isInProduction = config?.inProduction
      key = config?.productionAppKey
      secret = config?.productionAppSecret
      urlAllowListScopeOpenURL = config?.urlAllowListScopeOpenURL
      launchOptions = launch
      expectation.fulfill()
    }

    let _ = OGAirshipKit(
      airshipSecret: airshipSecretMock,
      airshipPushService: { OGAirshipPushServiceMock() },
      contactAssociationStore: OGAirshipContactAssociationStore.makeMock(),
      featureAdapter: airshipFeatureAdapterMock,
      isFlying: { false },
      setEnabled: { _ in },
      launchOptions: expectedLaunchOptions,
      takeOff: takeOff,
      inboxServicesSetup: { (OGAirshipInboxServiceMock(), OGAirshipMessageCenterCoordinatorMock()) }
    )
    await fulfillment(of: [expectation], timeout: 0.1)

    XCTAssertEqual(isInProduction, expectedIsInProduction)
    XCTAssertEqual(launchOptions as? [UIApplication.LaunchOptionsKey: String], expectedLaunchOptions)
    XCTAssertEqual(key, expectedKey)
    XCTAssertEqual(secret, expectedSecret)
    XCTAssertEqual(urlAllowListScopeOpenURL, expectedurlAllowListScopeOpenURL)
  }
}
