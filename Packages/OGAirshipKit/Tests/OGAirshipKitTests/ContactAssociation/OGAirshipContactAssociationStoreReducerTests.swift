import OGAirshipKitTestsUtils
import XCTest

@testable import OGAirshipKit

final class OGAirshipContactAssociationStoreReducerTests: XCTestCase {
  func test_WHEN_logOut_THEN_noUpdate() throws {
    var state = OGAirshipContactAssociationState.initial
    OGAirshipContactAssociationState.Reducer.reduce(
      &state,
      with: .logOut
    )
    XCTAssertEqual(
      OGAirshipContactAssociationState(isAwaitingUpdate: false, isOptedIn: false),
      state
    )
  }

  func test_WHEN_optIn_THEN_isOptedIn() throws {
    var state = OGAirshipContactAssociationState.initial
    OGAirshipContactAssociationState.Reducer.reduce(
      &state,
      with: .optIn
    )
    XCTAssertEqual(
      OGAirshipContactAssociationState(isAwaitingUpdate: false, isOptedIn: true),
      state
    )
  }

  func test_WHEN_optOut_THEN_isNotOptedIn() throws {
    var state = OGAirshipContactAssociationState.initial
    OGAirshipContactAssociationState.Reducer.reduce(
      &state,
      with: .optOut
    )
    XCTAssertEqual(
      OGAirshipContactAssociationState(isAwaitingUpdate: false, isOptedIn: false),
      state
    )
  }
}
