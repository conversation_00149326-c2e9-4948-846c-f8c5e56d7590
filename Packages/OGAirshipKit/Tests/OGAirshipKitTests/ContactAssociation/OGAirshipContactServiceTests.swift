import OGAirshipKitTestsUtils
import XCTest

@testable import OGAirshipKit

final class OGAirshipContactServiceTests: XCTestCase {
  func test_WHEN_resetContact_airshipResetContactCalled() async throws {
    let exp = expectation(description: "Airship reset contact called")

    var resetContactCallCount = 0
    let sut = OGAirshipContactService(
      attributesEditor: AttributesEditorMock(),
      contact: ContactEditorMock(),
      resetContact: {
        resetContactCallCount += 1
        exp.fulfill()
      },
      tagsEditor: TagGroupsEditorMock()
    )

    sut.resetContact()
    await fulfillment(of: [exp], timeout: 0.1)

    XCTAssertEqual(1, resetContactCallCount)
  }
}
