import Foundation
import OGAirshipKit
import OGCoreTestsUtils
import OGIdentifier

extension String {
  public static let stubRead = "stubRead"
  public static let stubNotRead = "stubNotRead"
  public static let stubDistantFuture = "stubDistantFuture"
  public static let stubDistantPast = "stubDistantPast"
}

extension OGAirshipInboxMessage {
  public static let stub = OGAirshipInboxMessage(
    id: Identifier(value: .stub),
    title: .stub,
    sender: .stub,
    summary: .stub,
    date: .distantPast,
    expirationDate: .distantFuture,
    wasRead: false,
    messageBodyURL: .stub
  )

  public static let stubNotRead = OGAirshipInboxMessage(
    id: Identifier(value: .stubNotRead),
    title: .stub,
    sender: .stub,
    summary: .stub,
    date: .distantPast,
    expirationDate: .distantFuture,
    wasRead: false,
    messageBodyURL: .stub
  )

  public static let stubRead = OGAirshipInboxMessage(
    id: Identifier(value: .stubRead),
    title: .stub,
    sender: .stub,
    summary: .stub,
    date: .distantPast,
    expirationDate: .distantFuture,
    wasRead: false,
    messageBodyURL: .stub
  )

  public static let stubDistantFuture = OGAirshipInboxMessage(
    id: Identifier(value: .stubDistantFuture),
    title: .stub,
    sender: .stub,
    summary: .stub,
    date: .distantFuture,
    expirationDate: .distantFuture,
    wasRead: false,
    messageBodyURL: .stub
  )

  public static let stubDistantPast = OGAirshipInboxMessage(
    id: Identifier(value: .stubDistantPast),
    title: .stub,
    sender: .stub,
    summary: .stub,
    date: .distantPast,
    expirationDate: .distantFuture,
    wasRead: false,
    messageBodyURL: .stub
  )
}

extension [OGAirshipInboxMessage] {
  public static let stub: [Element] = [.stubRead, .stubNotRead]
}
