import Foundation
import OGAirshipKit

public struct InboxFeatureConfig: InboxFeatureConfigurable {
  public var isEnabled: Bool
  public var deleteMessagesAfterDays: Int
  public var deleteMessagesAfterTenantChange: Bool
  public var forceMessageWebView: Bool
  public var shouldShowThumbnails: Bool
  public var supportedUrls: [URL]

  public static var stub: InboxFeatureConfigurable {
    InboxFeatureConfig(
      isEnabled: true,
      deleteMessagesAfterDays: 1,
      deleteMessagesAfterTenantChange: true,
      forceMessageWebView: false,
      shouldShowThumbnails: false,
      supportedUrls: []
    )
  }
}
