import OGAirshipKit
import OGDomainStore
import OGDomainStoreTestsUtils
import OGMacros

extension OGAirshipContactAssociationStore {
  public static func makeMock(
    initialState: OGAirshipContactAssociationState = .initial,
    reducerMock: OGDomainReducerMock<State, Action> = OGDomainReducerMock(),
    middlewareMock: OGDomainMiddlewareMock<State, Action> = OGDomainMiddlewareMock()
  )
    -> OGAirshipContactAssociationStore {
    OGAirshipContactAssociationStore(
      initialState: initialState,
      reducer: reducerMock.reduce,
      middlewares: middlewareMock
    )
  }

  public static var mock: OGDomainStoreMock<OGAirshipContactAssociationState, OGAirshipContactAssociationAction> {
    OGDomainStoreMock<OGAirshipContactAssociationState, OGAirshipContactAssociationAction>()
  }
}
