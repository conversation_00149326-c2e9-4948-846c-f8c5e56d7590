import Combine
import Foundation
import OGAirshipKit
import OGMacros
import OGMock
import WebKit

@OGMock
public final class OGAirshipContactServiceMock: OGAirshipContactServing {
  public init() {}

  public func resetContact() {
    mock.resetContact()
  }

  public func setAttributes(_ attribute: [OGAirshipAttribute]) {
    mock.setAttributes(attribute)
  }

  public func setTagGroups(_ attribute: [OGAirshipAttribute]) {
    mock.setTagGroups(attribute)
  }

  public func setContact(_ value: String?) {
    mock.setContact(value)
  }
}
