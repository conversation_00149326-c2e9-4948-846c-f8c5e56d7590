import Combine
import Foundation
import OGAirshipKit
import OGCore
import OGDIService
import OGMacros
import OGMock

// MARK: - AirshipTrackingFeatureAdapterMock

@OGMock
public final class AirshipTrackingFeatureAdapterMock: AirshipTrackingFeatureAdaptable {
  public init() {}
  public var configuration: CurrentValueSubject<any AirshipTrackingFeatureConfigurable, Never> {
    get {
      mock.configuration.getter.record()
    }
    set {
      mock.configuration.setter.record(newValue)
    }
  }

  public var isAdaptedFeatureEnabled: Bool {
    get {
      mock.isAdaptedFeatureEnabled.getter.record()
    }
    set {
      mock.isAdaptedFeatureEnabled.setter.record(newValue)
    }
  }

  public init(
    isEnabled: Bool = true,
    keepContactAssociation: Bool = true
  ) {
    mock.configuration.getter.mockCall { _ in
      CurrentValueSubject(
        AirshipTrackingFeatureConfiguration(
          isEnabled: isEnabled,
          keepContactAssociation: keepContactAssociation
        )
      )
    }
  }
}

// MARK: - OGAirshipTrackingFeatureAdapterContainer + AutoRegistering

extension OGAirshipTrackingFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
    guard OGCoreContainer.shared.appEnvironment().isTestsBuild else { return }
    airshipTracking.register {
      AirshipTrackingFeatureAdapterMock(keepContactAssociation: true)
    }
  }
}
