import Combine
import Foundation
import O<PERSON>irshipKit
import OGAppEnvironment
import OGCore
import OGDIService
import OGMacros
import OGMock

// MARK: - OGAirshipContainer + AutoRegistering

extension OGAirshipContainer: AutoRegistering {
  @MainActor
  public func autoRegister() {
    guard OGCoreContainer.shared.appEnvironment().isTestsBuild else { return }

    airshipKit.register {
      OGAirshipKit(
        airshipConfig: OGAirshipConfigurationStub(),
        airshipSecret: OGAirshipSecretStub(),
        appEnvironment: OGAppEnvironment(isDebugOrBetaBuild: false, isDebugBuild: true),
        featureAdapter: OGAirshipFeatureAdapterMock(),
        isFlying: ({ true }),
        setEnabled: ({ _ in }),
        launchOptions: nil,
        takeOff: ({ _, _ in

        })
      )
    }

    contactAssociationStore.register {
      OGAirshipContactAssociationStore.makeMock()
    }

    contactService.register {
      OGAirshipContactServiceMock()
    }

    deviceIdentifier.register {
      let deviceIdentifier = OGAirshipAssociatedDeviceIdentierMock()
      deviceIdentifier.mock.currentAssociatedDeviceIdentifiersCalls.mockCall { _ in
        AssociatedIdentifiers.identifiers()
      }
      return deviceIdentifier
    }

    inboxStore.register {
      OGAirshipInboxStore.makeMock()
    }

    inboxService.register {
      OGAirshipInboxServiceMock()
    }

    inboxCoordinator.register {
      OGAirshipMessageCenterCoordinatorMock()
    }

    channelService.register {
      OGAirshipChannelService()
    }

    privacyManager.register {
      let privacyManager = OGAirshipPrivacyManagerMock()
      privacyManager.mock.disableFeaturesCalls.mockCall { _ in }
      return OGAirshipPrivacyManagerMock()
    }
  }
}
