import Combine
import Foundation
import OGAirshipKit
import OGMacros
import OGMock
import WebKit

@OGMock
public final class OGAirshipInboxServiceMock: OGAirshipInboxServing {
  public init() {}

  public func deleteMessages(with ids: [String]) async {
    await mock.deleteMessages(with: ids)
  }

  public func deleteMessage(with id: String) async {
    await mock.deleteMessage(with: id)
  }

  public func markRead(messageId: String) async {
    await mock.markRead(messageId: messageId)
  }

  public func refreshMessages() async -> Bool {
    await mock.refreshMessages()
  }

  public func watchMessages() -> AnyPublisher<[OGAirshipInboxMessage], Never> {
    mock.watchMessages()
  }

  public func watchUnreadCount() -> AnyPublisher<Int, Never> {
    mock.watchUnreadCount()
  }
}
