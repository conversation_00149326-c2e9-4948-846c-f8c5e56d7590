import AirshipCore
import Combine
import Foundation
import OGAirshipKit
import OGCore
import OGDIService
import OGMacros
import OGMock

// MARK: - OGAirshipChannelServing

@OGMock
public final class OGAirshipChannelServiceMock: OGAirshipChannelServing {
  public func setLoggedInTag(_ value: Bool) {
    mock.setLoggedInTag(value)
  }

  public func setAttribute(_ attribute: OGAirshipAttribute) {
    mock.setAttribute(attribute)
  }

  public func set(attributes: [String: Any]) {
    mock.set(attributes: attributes)
  }

  public func remove(keys: [String]) {
    mock.remove(keys: keys)
  }

  public init() {}
}
