import AirshipCore
import Combine
import Foundation
import OGAirshipKit
import OGCore
import OGDIService
import OGMacros
import <PERSON>GMock

@OGMock
public final class OGAirshipAssociatedDeviceIdentierMock: AirshipAssociatedDeviceIdentifing {
  public init() {}

  public func associateDeviceIdentifiers(_ associatedIdentifiers: AssociatedIdentifiers) {
    mock.associateDeviceIdentifiers(associatedIdentifiers)
  }

  public func currentAssociatedDeviceIdentifiers() -> AssociatedIdentifiers {
    mock.currentAssociatedDeviceIdentifiers()
  }
}
