import Foundation
import OGDIService

// MARK: - OGAirshipContainer

public final class OGAirshipContainer: OGDISharedContainer {
  public static var shared: OGAirshipContainer = .init()
  public var manager: OGDIContainerManager = .init()

  public var airshipKit: OGDIService<OGAirshipKit?> {
    self {
      nil
    }.cached
  }

  public var analytics: OGDIService<OGAirshipAnalyticsLogging?> {
    self {
      nil
    }.cached
  }

  public var deviceIdentifier: OGDIService<AirshipAssociatedDeviceIdentifing?> {
    self {
      nil
    }
  }

  public var inboxStore: OGDIService<OGAirshipInboxStore?> {
    self {
      nil
    }.cached
  }

  public var contactAssociationStore: OGDIService<OGAirshipContactAssociationStore?> {
    self {
      nil
    }.cached
  }

  public var channelStore: OGDIService<OGAirshipChannelStore?> {
    self {
      nil
    }.cached
  }

  public var pushService: OGDIService<OGAirshipPushServing?> {
    self {
      nil
    }.cached
  }

  public var inboxService: OGDIService<OGAirshipInboxServing?> {
    self {
      nil
    }.cached
  }

  public var inboxCoordinator: OGDIService<OGAirshipMessageCenterCoordinable?> {
    self {
      nil
    }.cached
  }

  public var channelService: OGDIService<OGAirshipChannelServing?> {
    self {
      nil
    }
  }

  public var contactService: OGDIService<OGAirshipContactServing?> {
    self {
      nil
    }
  }

  public var privacyManager: OGDIService<AirshipPrivacyManaging?> {
    self {
      nil
    }
  }

  public var channelID: OGDIService<OGAirshipChannelIdDialogFeatureAdaptable?> {
    self {
      nil
    }.cached
  }

  public var inboxButtonTapPublisher: OGDIService<OGInboxButtonTapPublishing> {
    self {
      OGInboxButtonTapPublisher()
    }.cached
  }
}
