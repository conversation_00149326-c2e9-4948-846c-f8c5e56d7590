import AirshipCore
// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Combine
import Foundation
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import OGMacros

// MARK: - OGAirshipChannelIdDialogConfigurable

public protocol OGAirshipChannelIdDialogConfigurable {
  var isEnabled: Bool { get set }
  var channelID: String? { get set }
  var numberOfTaps: Int { get set }
}

// MARK: - AirshipChannelIdDialogConfiguration

public struct AirshipChannelIdDialogConfiguration: OGAirshipChannelIdDialogConfigurable {
  public var isEnabled: Bool = true
  public var channelID: String? = nil
  public var numberOfTaps: Int = 4
  public init() {}
}

// MARK: - OGAirshipChannelIdDialogFeatureAdapter

public final class OGAirshipChannelIdDialogFeatureAdapter: OGFeatureAdapter, OGAirshipChannelIdDialogFeatureAdaptable {
  override public class var featureName: OGFeature.Name { OGIdentifier.airshipChannelIdDialog.value }

  public let configuration: CurrentValueSubject<OGAirshipChannelIdDialogConfigurable, Never>
  private var subscriptions = Set<AnyCancellable>()

  public init(configuration: OGAirshipChannelIdDialogConfigurable = AirshipChannelIdDialogConfiguration()) {
    self.configuration = CurrentValueSubject(configuration)
    super.init()
    receiveUpdates()
  }

  private func receiveUpdates() {
    $feature.sink { [weak self] feature in
      guard let self else { return }
      var updatedConfiguration = self.configuration.value
      guard let feature else {
        self.configuration.send(updatedConfiguration)
        return
      }
      updatedConfiguration.isEnabled = feature.isEnabled

      let numberOfTaps: Int = (feature.customValue(for: OGFeatureKey.CustomValues.AirshipChannelIdDialog.numberOfTaps)) ?? self.configuration.value.numberOfTaps
      updatedConfiguration.numberOfTaps = numberOfTaps

      updatedConfiguration.channelID = Airship.channel.identifier

      self.configuration.send(updatedConfiguration)
    }.store(in: &subscriptions)
  }
}

// MARK: - OGAirshipChannelIdDialogFeatureAdaptable

public protocol OGAirshipChannelIdDialogFeatureAdaptable: OGFeatureAdaptable {
  var configuration: CurrentValueSubject<OGAirshipChannelIdDialogConfigurable, Never> { get }
}

// MARK: - OGFeatureKey.CustomValues.AirshipChannelIdDialog

extension OGFeatureKey.CustomValues {
  public enum AirshipChannelIdDialog: String, OGKeyReceivable {
    public var value: String {
      rawValue
    }

    case numberOfTaps = "numberOfTapsToTrigger"
  }
}

extension OGIdentifier {
  public static let airshipChannelIdDialog = #identifier("airshipChannelIdDialog")
}
