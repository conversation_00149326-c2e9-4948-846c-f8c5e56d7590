import AirshipCore

// MARK: - AirshipAssociatedDeviceIdentifing

public protocol AirshipAssociatedDeviceIdentifing: AnyObject {
  func associateDeviceIdentifiers(_ associatedIdentifiers: AssociatedIdentifiers)
  func currentAssociatedDeviceIdentifiers() -> AssociatedIdentifiers
}

// MARK: - AirshipAnalytics + AirshipAssociatedDeviceIdentifing

extension AirshipAnalytics: AirshipAssociatedDeviceIdentifing {}
