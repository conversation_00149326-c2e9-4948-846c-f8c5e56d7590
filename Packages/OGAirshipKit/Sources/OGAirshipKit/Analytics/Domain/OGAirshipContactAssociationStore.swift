import Combine
import OGDomainStore

public typealias OGAirshipContactAssociationStore = OGDomainStore<OGAirshipContactAssociationState, OGAirshipContactAssociationAction>

extension OGDomainStore where State == OGAirshipContactAssociationState, Action == OGAirshipContactAssociationAction {
  public static func make() -> OGAirshipContactAssociationStore {
    OGAirshipContactAssociationStore(
      reducer: OGAirshipContactAssociationState.Reducer.reduce,
      middlewares: OGAirshipContactAssociationState.Middleware()
    )
  }
}

// MARK: - OGAirshipContactAssociationState

public struct OGAirshipContactAssociationState: OGDomainState, Equatable {
  public private(set) var isAwaitingUpdate: Bool = false
  private(set) var isOptedIn: Bool = false

  mutating func set(isOptedIn: Bool) {
    self.isOptedIn = isOptedIn
  }

  public static let initial: Self = .init()
}

// MARK: - OGAirshipContactAssociationAction

public enum OGAirshipContactAssociationAction: OGDomainAction, Equatable {
  case logOut
  case optIn
  case optOut
}

// MARK: - Reducer & Midleware

extension OGAirshipContactAssociationState {
  public enum Reducer: OGDomainActionReducible {
    public static func reduce(
      _ state: inout OGAirshipContactAssociationState,
      with action: OGAirshipContactAssociationAction
    ) {
      switch action {
      case .logOut:
        break
      case .optIn:
        state.set(isOptedIn: true)
      case .optOut:
        state.set(isOptedIn: false)
      }
    }
  }

  public struct Middleware: OGDomainMiddleware {
    private let contactService: OGAirshipContactServing?
    private let airshipTrackingFeature: AirshipTrackingFeatureAdaptable

    init(
      airshipTrackingFeature: AirshipTrackingFeatureAdaptable = OGAirshipTrackingFeatureAdapterContainer.shared.airshipTracking(),
      contactService: OGAirshipContactServing? = OGAirshipContainer.shared.contactService()
    ) {
      self.airshipTrackingFeature = airshipTrackingFeature
      self.contactService = contactService
    }

    public func callAsFunction(
      action: OGAirshipContactAssociationAction,
      for _: OGAirshipContactAssociationState
    ) async throws
      -> OGAirshipContactAssociationAction? {
      let shouldKeepAssociation = airshipTrackingFeature.configuration.value.keepContactAssociation
      switch action {
      case .logOut where !shouldKeepAssociation, .optOut:
        contactService?.resetContact()
        return nil

      case .logOut, .optIn:
        return nil
      }
    }
  }
}
