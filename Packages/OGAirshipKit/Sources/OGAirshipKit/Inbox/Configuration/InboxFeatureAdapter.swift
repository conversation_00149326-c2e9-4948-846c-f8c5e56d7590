// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Combine
import Foundation
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import <PERSON>GMacros

// MARK: - InboxFeatureConfigurable

public protocol InboxFeatureConfigurable {
  var isEnabled: Bool { get set }

  var deleteMessagesAfterDays: Int { get set }
  var deleteMessagesAfterTenantChange: Bool { get set }
  var forceMessageWebView: Bool { get set }
  var shouldShowThumbnails: Bool { get set }
  var supportedUrls: [URL] { get set }
}

// MARK: - InboxFeatureAdapter

public final class InboxFeatureAdapter: OGFeatureAdapter, InboxFeatureAdaptable {
  override public class var featureName: OGFeature.Name { OGIdentifier.inbox.value }

  public let configuration: CurrentValueSubject<InboxFeatureConfigurable, Never>
  private var subscriptions = Set<AnyCancellable>()

  public init(configuration: InboxFeatureConfigurable?) {
    guard let configuration else {
      fatalError("The InboxFeatureConfiguration has not been registered")
    }
    self.configuration = CurrentValueSubject(configuration)
    super.init()

    receiveUpdates()
  }

  private func receiveUpdates() {
    $feature.sink { [weak self] feature in
      guard let self else { return }
      var updatedConfiguration = self.configuration.value
      guard let feature else {
        updatedConfiguration.isEnabled = false
        self.configuration.send(updatedConfiguration)
        return
      }
      updatedConfiguration.isEnabled = feature.isEnabled

      let deleteMessagesAfterDays: Int = (feature.customValue(for: OGFeatureKey.CustomValues.Inbox.deleteMessagesAfterDays)) ?? self.configuration.value.deleteMessagesAfterDays
      updatedConfiguration.deleteMessagesAfterDays = deleteMessagesAfterDays
      let deleteMessagesAfterTenantChange: Bool = (feature.customValue(for: OGFeatureKey.CustomValues.Inbox.deleteMessagesAfterTenantChange)) ?? self.configuration.value.deleteMessagesAfterTenantChange
      updatedConfiguration.deleteMessagesAfterTenantChange = deleteMessagesAfterTenantChange
      let forceMessageWebView: Bool = (feature.customValue(for: OGFeatureKey.CustomValues.Inbox.forceMessageWebView)) ?? self.configuration.value.forceMessageWebView
      updatedConfiguration.forceMessageWebView = forceMessageWebView
      let shouldShowThumbnails: Bool = (feature.customValue(for: OGFeatureKey.CustomValues.Inbox.shouldShowThumbnails)) ?? self.configuration.value.shouldShowThumbnails
      updatedConfiguration.shouldShowThumbnails = shouldShowThumbnails
      let supportedUrls: [URL] = (feature.customValue(for: OGFeatureKey.CustomValues.Inbox.supportedUrls)) ?? self.configuration.value.supportedUrls
      updatedConfiguration.supportedUrls = supportedUrls
      self.configuration.send(updatedConfiguration)
    }.store(in: &subscriptions)
  }
}

// MARK: - InboxFeatureAdaptable

public protocol InboxFeatureAdaptable: OGFeatureAdaptable {
  var configuration: CurrentValueSubject<InboxFeatureConfigurable, Never> { get }
}

// MARK: - OGFeatureKey.CustomValues.Inbox

extension OGFeatureKey.CustomValues {
  public enum Inbox: String, OGKeyReceivable {
    public var value: String {
      rawValue
    }

    case deleteMessagesAfterDays
    case deleteMessagesAfterTenantChange
    case forceMessageWebView
    case shouldShowThumbnails
    case supportedUrls
  }
}

extension OGIdentifier {
  public static let inbox = #identifier("inbox")
  public static let inboxDetail = #identifier("inbox/detail")
}
