import AirshipMessageCenter
import Foundation
import OGIdentifier

// MARK: - OGAirshipInboxMessage

public struct OGAirshipInboxMessage: Equatable, Hashable, Identifiable, Sendable {
  public let id: Identifier<Self>
  public var title: String
  public var sender: String
  public var summary: String?
  public var date: Date
  public var expirationDate: Date?
  public var wasRead: Bool
  public var messageBodyURL: URL?
  public var listIcon: String?

  /// temp
  public init(
    id: Identifier<Self>,
    title: String,
    sender: String,
    summary: String? = nil,
    date: Date,
    expirationDate: Date? = nil,
    wasRead: Bool,
    messageBodyURL: URL? = nil,
    listIcon: String? = nil
  ) {
    self.id = id
    self.title = title
    self.sender = sender
    self.summary = summary
    self.date = date
    self.expirationDate = expirationDate
    self.wasRead = wasRead
    self.messageBodyURL = messageBodyURL
    self.listIcon = listIcon
  }
}

extension Collection<MessageCenterMessage> {
  var toMessages: [OGAirshipInboxMessage] {
    map(\.toMessage)
  }
}

extension MessageCenterMessage {
  var toMessage: OGAirshipInboxMessage {
    OGAirshipInboxMessage(
      id: Identifier(value: id),
      title: title,
      sender: extra["sender"] ?? "",
      summary: extra["summary"],
      date: sentDate,
      expirationDate: expirationDate,
      wasRead: !unread,
      messageBodyURL: bodyURL,
      listIcon: listIcon
    )
  }
}
