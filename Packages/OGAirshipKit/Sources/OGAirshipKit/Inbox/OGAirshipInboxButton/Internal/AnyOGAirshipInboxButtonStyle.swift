// swiftlint:disable type_name

import SwiftUI

struct AnyOGAirshipInboxButtonStyle: OGAirshipInboxButtonStyle {
  private let _makeBody: (Configuration) -> AnyView

  init(style: some OGAirshipInboxButtonStyle) {
    self._makeBody = { configuration in
      AnyView(style.makeBody(configuration: configuration))
    }
  }

  func makeBody(configuration: Configuration) -> some View {
    _makeBody(configuration)
  }
}
