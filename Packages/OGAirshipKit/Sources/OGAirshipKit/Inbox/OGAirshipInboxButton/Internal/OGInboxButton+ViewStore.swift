
import Combine
import OGCore
import OGDIService
import OGDomainStore
import OGRouter
import OGViewStore

// MARK: - OGAirshipInboxButton.Store

extension OGAirshipInboxButton {
  typealias Store = OGViewStore<ViewState, Event>
}

extension OGAirshipInboxButton {
  @MainActor
  static func make() -> Store {
    OGAirshipInboxButton.Store(
      reducer: Reducer.reduce,
      middleware: Middleware(),
      connector: Connector()
    )
  }
}

// MARK: - OGAirshipInboxButton.Event

extension OGAirshipInboxButton {
  enum Event: OGViewEvent {
    case didTap

    /// Private events
    case _enabled(Bool)
    case _updated(unreadCount: Int)
  }
}

// MARK: - OGAirshipInboxButton.ViewState

extension OGAirshipInboxButton {
  struct ViewState: OGViewState {
    private(set) var unreadCount: Int = 0
    private(set) var isEnabled: Bool = true
    static var initial: OGAirshipInboxButton.ViewState = .init()

    mutating func update(unreadCount: Int) {
      self.unreadCount = unreadCount
    }

    mutating func isEnabled(_ isEnabled: Bool) {
      self.isEnabled = isEnabled
    }
  }
}

extension OGAirshipInboxButton {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _ state: inout ViewState,
      with event: Event
    ) {
      switch event {
      case .didTap:
        break

      // Private events
      case let ._updated(unreadCount):
        state.update(unreadCount: unreadCount)
      case let ._enabled(isEnabled):
        state.isEnabled(isEnabled)
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let router: OGRoutePublishing
    private let inboxButtonTapPublisher: OGInboxButtonTapPublishing

    init(
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      inboxButtonTapPublisher: OGInboxButtonTapPublishing = OGAirshipContainer.shared.inboxButtonTapPublisher()
    ) {
      self.router = router
      self.inboxButtonTapPublisher = inboxButtonTapPublisher
    }

    func callAsFunction(
      event: Event,
      for _: ViewState
    ) async
      -> Event? {
      switch event {
      case .didTap:
        inboxButtonTapPublisher.didTap()
        router.send(OGRoute(OGIdentifier.inbox.value))
        return nil

      // Private events
      case ._enabled, ._updated:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private var cancellables = Set<AnyCancellable>()
    private var inboxStore: OGAirshipInboxStore?
    private var inboxFeature: InboxFeatureAdaptable

    init(
      inboxStore: OGAirshipInboxStore? = OGAirshipContainer.shared.inboxStore(),
      inboxFeature: InboxFeatureAdaptable = InboxFeatureAdapterContainer.shared.inbox()
    ) {
      self.inboxStore = inboxStore
      self.inboxFeature = inboxFeature
    }

    func configure(
      dispatch: @escaping (Event) async -> Void
    ) async {
      inboxFeature
        .configuration
        .sink { configuation in
          Task {
            await dispatch(._enabled(configuation.isEnabled))
          }
        }
        .store(in: &cancellables)

      await inboxStore?
        .watch(keyPath: \.unreadCount)
        .sink { unreadCount in
          Task {
            await dispatch(._updated(unreadCount: unreadCount))
          }
        }
        .store(in: &cancellables)
    }
  }
}
