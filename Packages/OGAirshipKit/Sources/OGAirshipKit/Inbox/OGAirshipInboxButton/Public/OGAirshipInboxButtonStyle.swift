import SwiftUI

/// A type that applies custom appearance to
/// all OGAirshipInboxButtons within a view hierarchy.
public protocol OGAirshipInboxButtonStyle {
  /// A view that represents the body of a OGAirshipInboxButton.
  associatedtype Body: View

  /// The properties of a OGAirshipInboxButton.
  typealias Configuration = OGAirshipInboxButtonStyleConfiguration

  /// Creates a view that represents the body of a OGAirshipInboxButton.
  ///
  /// The system calls this method for each OGAirshipInboxButton instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGAirshipInboxButtonStyle``.
  /// ```swift
  /// struct MyCustomOGAirshipInboxButtonStyle: OGAirshipInboxButtonStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGAirshipInboxButtonConfiguration`` of the  OGAirshipInboxButton.
  /// - Returns: A view that represents a OGAirshipInboxButton.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
