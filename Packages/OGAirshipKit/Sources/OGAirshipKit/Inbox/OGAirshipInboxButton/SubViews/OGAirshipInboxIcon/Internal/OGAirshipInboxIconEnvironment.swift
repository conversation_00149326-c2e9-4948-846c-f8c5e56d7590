// swiftlint:disable type_name

import SwiftUI

// MARK: - OGAirshipInboxIconStyleKey

struct OGAirshipInboxIconStyleKey: EnvironmentKey {
  static var defaultValue = AnyOGAirshipInboxIconStyle(style: DefaultOGAirshipInboxIconStyle())
}

extension EnvironmentValues {
  var styleOGAirshipInboxIcon: AnyOGAirshipInboxIconStyle {
    get { self[OGAirshipInboxIconStyleKey.self] }
    set { self[OGAirshipInboxIconStyleKey.self] = newValue }
  }
}
