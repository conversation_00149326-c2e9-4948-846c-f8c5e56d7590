// swiftlint:disable type_name

import SwiftUI

struct AnyOGAirshipInboxIconBadgeStyle: OGAirshipInboxIconBadgeStyle {
  private let _makeBody: (Configuration) -> AnyView

  init(style: some OGAirshipInboxIconBadgeStyle) {
    self._makeBody = { configuration in
      AnyView(style.makeBody(configuration: configuration))
    }
  }

  func makeBody(configuration: Configuration) -> some View {
    _makeBody(configuration)
  }
}
