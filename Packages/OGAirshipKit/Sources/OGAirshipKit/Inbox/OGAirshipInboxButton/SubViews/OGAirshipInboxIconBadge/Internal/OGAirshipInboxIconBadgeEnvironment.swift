// swiftlint:disable type_name

import SwiftUI

// MARK: - OGAirshipInboxIconBadgeStyleKey

struct OGAirshipInboxIconBadgeStyleKey: EnvironmentKey {
  static var defaultValue = AnyOGAirshipInboxIconBadgeStyle(style: DefaultOGAirshipInboxIconBadgeStyle())
}

extension EnvironmentValues {
  var styleOGAirshipInboxIconBadge: AnyOGAirshipInboxIconBadgeStyle {
    get { self[OGAirshipInboxIconBadgeStyleKey.self] }
    set { self[OGAirshipInboxIconBadgeStyleKey.self] = newValue }
  }
}
