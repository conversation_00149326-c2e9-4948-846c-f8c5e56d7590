import Combine
import OGDIService
import OGNetworkLogger
import SwiftUI

// MARK: - TabType

public enum TabType: String {
  case appInformation
  case snapshot
  case logs
}

// MARK: - OGDetectiveProtocol

public protocol OGDetectiveProtocol {
  /// Sets the order of modules within each specified tab.
  /// - Parameter moduleOrder: A dictionary mapping a tab to an array of `CustomModuleOrderProtocol` items.
  func setModuleOrder(_ moduleOrder: [TabType: [CustomModuleOrderProtocol]])

  /// Appends modules to an existing tab.
  /// - Parameter modules: A dictionary mapping a tab to an array of `any OGDetectiveModuleProtocol` items to be appended.
  func appendModulesToTab(_ modules: [TabType: [any OGDetectiveModuleProtocol]])

  /// Replaces an existing tab with new modules or adds a new tab if it doesn't already exist.
  /// - Parameter tabModules: A dictionary mapping a tab to an array of `any OGDetectiveModuleProtocol` items.
  func addTabWithModules(_ tabModules: [TabType: [any OGDetectiveModuleProtocol]])
}

// MARK: - OGDetective

internal final class OGDetective: ObservableObject, OGDetectiveProtocol {
  @OGInjected(\OGDetectiveContainer.appInformationModule) private var appInformationModule
  @OGInjected(\OGDetectiveContainer.networkModule) private var networkModule
  @OGInjected(\OGDetectiveContainer.logModule) private var logModule
  @OGInjected(\OGDetectiveContainer.eventModule) private var eventModule
  @OGInjected(\OGDetectiveContainer.snapshotModule) private var snapshotModule
  @OGInjected(\OGDetectiveContainer.snapshotDistributor) private var snapshotDistributor
  @OGInjected(\OGDetectiveContainer.internalDetectiveService) private var internalDetectiveService

  private var cancellables: Set<AnyCancellable> = []

  internal init() {
    OGNetworkLoggerContainer.shared.networkLogger().startLogger()

    setModuleOrder([
      .logs: [DefaultModules.log, DefaultModules.network, DefaultModules.event],
      .snapshot: [DefaultModules.snapshot],
      .appInformation: [DefaultModules.appInformation]
    ])

    snapshotDistributor.newSnapshot.sink { [weak self] snapshot in
      self?.internalDetectiveService.tabModules.values.flatMap { $0 }.forEach { module in
        module.didTakeSnapshot(atDate: snapshot.date)
      }
    }.store(in: &cancellables)
  }

  public var modules: [CustomModuleOrderProtocol] {
    internalDetectiveService.tabModules.values.flatMap { $0 }
  }

  public func setModuleOrder(_ moduleOrder: [TabType: [CustomModuleOrderProtocol]]) {
    for (tab, modules) in moduleOrder {
      internalDetectiveService.tabModules[tab] = modules.compactMap { module in
        switch module {
        case let defaultModule as DefaultModules:
          switch defaultModule {
          case .appInformation:
            return appInformationModule
          case .snapshot:
            return snapshotModule
          case .network:
            return networkModule
          case .log:
            return logModule
          case .event:
            return eventModule
          }
        case let ogModule as any OGDetectiveModuleProtocol:
          return ogModule
        default:
          return nil
        }
      }
    }
  }

  public func appendModulesToTab(_ modules: [TabType: [any OGDetectiveModuleProtocol]]) {
    for (tab, moduleList) in modules {
      internalDetectiveService.tabModules[tab, default: []].append(contentsOf: moduleList)
    }
  }

  public func addTabWithModules(_ tabModules: [TabType: [any OGDetectiveModuleProtocol]]) {
    for (tab, moduleList) in tabModules {
      internalDetectiveService.tabModules[tab] = moduleList
    }
  }
}

// MARK: - InternalDetectiveServicable

internal protocol InternalDetectiveServicable {
  var tabModules: [TabType: [any OGDetectiveModuleProtocol]] { get set }
  func shareSnapshots(snapshots: [Snapshot], completion: @escaping () -> Void)
}

// MARK: - InternalDetectiveService

internal final class InternalDetectiveService: InternalDetectiveServicable {
  var tabModules: [TabType: [any OGDetectiveModuleProtocol]] = [:]

  func shareSnapshots(snapshots: [Snapshot], completion: @escaping () -> Void) {
    OGDetectiveSharingService().shareSnapshots(snapshots: snapshots, completion: completion)
  }
}
