import Combine
import OGCore
import SwiftUI

// MARK: - CustomModuleOrderProtocol

/// This protocol is a helper for defining a custom order of internal and custom modules.
public protocol CustomModuleOrderProtocol {}

// MARK: - DefaultModules

/// Enum to set the order of the internal modules
public enum DefaultModules: CustomModuleOrderProtocol, Equatable {
  case appInformation
  case snapshot
  case network
  case log
  case event
}

/// This protocol needs to be implemented for any module that should be displayed and/or should export data for a given snapshot.
public typealias OGDetectiveModuleProtocol = OGDetectiveModuleSkeletonProtocol & Identifiable & CustomModuleOrderProtocol & AnyObject

// MARK: - OGDetectiveModuleSkeletonProtocol

/// This protocol defines the business logic of a module that is displayed in the user interface.
public protocol OGDetectiveModuleSkeletonProtocol {
  associatedtype DashboardContent: View
  associatedtype SnapshotContent: View
  /// Identifier that is needed for building views
  var identifier: UUID { get }
  /// The name the potential exported file should have.
  var exportFileName: String { get set }
  /// This function is called when processing a snapshot to obtain the data to be exported. If nil get's returned, no data will be exported.
  /// - Parameter date: The date for the snapshot that will be exported.
  /// - Returns: The string that will be represented in a file. If nil, it will not be exported.
  func exportData(forDate date: Date) -> String?
  /// Notifies the module that a snapshot has been taken, allowing it to prepare the export data later.
  /// - Parameter date: The date at which the snapshot was taken.
  func didTakeSnapshot(atDate date: Date)
  /// Is called to display the module in the dashboard.
  /// - Returns: The view that is displayed in the dashboard
  @ViewBuilder
  func dashboardView() -> DashboardContent
  /// Gets called to display the module in the snapshot view.
  /// - Parameter date: the date of the snapshot
  /// - Returns: The view that is displayed in the snapshot view.
  @ViewBuilder
  func snapshotView(forSnapshotDate date: Date) -> SnapshotContent
}

extension OGDetectiveModuleSkeletonProtocol where Self: Identifiable {
  public var exportFileName: String {
    get {
      String(describing: type(of: self))
    }
    set {}
  }

  var id: UUID { UUID() }

  func exportData(forDate _: Date) -> String? { nil }
}
