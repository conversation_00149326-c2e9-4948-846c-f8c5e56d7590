import SwiftUI

// MARK: - OGDetectiveSearchBar

struct OGDetectiveSearchBar: View {
  @Binding var searchText: String

  var body: some View {
    HStack {
      Image(systemName: "magnifyingglass")
        .foregroundColor(Theme.Token.Colors.background_40)
      TextField("Search", text: $searchText)
        .foregroundColor(Theme.Token.Colors.text_dark_80)
      if !searchText.isEmpty {
        Button {
          searchText = ""
        } label: {
          Image(systemName: "xmark.circle.fill")
            .foregroundColor(Theme.Token.Colors.background_40)
        }
      }
    }
    .padding()
    .background(Theme.Token.Colors.background_15)
    .cornerRadius(10)
  }
}

// MARK: - OGDetectiveSearchBar_Previews

struct OGDetectiveSearchBar_Previews: PreviewProvider {
  static var previews: some View {
    OGDetectiveSearchBar(searchText: Binding(get: { "" }, set: { _ in }))
  }
}
