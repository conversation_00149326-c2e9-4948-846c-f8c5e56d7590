import SwiftUI

// MARK: - OGDetectiveSectionCard

public struct OGDetectiveSectionCard<Content: View, DetailContent: View>: View {
  let headline: String
  let content: () -> Content
  let detailView: (() -> DetailContent)?

  public init(
    headline: String,
    @ViewBuilder content: @escaping () -> Content,
    detailView: (() -> DetailContent)? = nil
  ) {
    self.headline = headline
    self.content = content
    self.detailView = detailView
  }

  public var body: some View {
    VStack {
      VStack(spacing: 4) {
        headlineRow
        content()
      }
      .padding()
      .background(Theme.Token.Colors.background_0)
      .cornerRadius(12)
    }
    .background(Color.clear)
  }

  var headlineTitle: some View {
    HStack {
      Text(headline)
        .titelSBold()
        .foregroundColor(Theme.Token.Colors.text_dark)
      Spacer()
    }
  }

  @ViewBuilder var headlineRow: some View {
    if let detailView {
      NavigationLink(destination: detailView()) {
        HStack {
          headlineTitle
          Theme.Token.Icons.chevronRight
            .renderingMode(.template)
            .foregroundColor(Theme.Token.Colors.text_dark)
        }
      }
      .isDetailLink(false)
    } else {
      headlineTitle
    }
  }
}

// MARK: - OGDetectiveSectionCard_Previews

struct OGDetectiveSectionCard_Previews: PreviewProvider {
  static var previews: some View {
    OGDetectiveSectionCard(headline: "Something cool", content: {
      Text("Some textSome textSome textSome textSome textSome textSome textSome textSome textSome textSome textSome textSome text")
      Text("Some text")
      Text("Some text")
      Text("Some text")
    }) {
      Text("DetailView")
    }
  }
}
