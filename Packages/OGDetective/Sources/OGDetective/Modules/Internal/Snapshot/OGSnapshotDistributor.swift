import Combine
import OGDIService
import SwiftUI

// MARK: - SnapshotDistributable

protocol SnapshotDistributable {
  var newSnapshot: PassthroughSubject<Snapshot, Never> { get }
  var snapshotsPublisher: Published<[Snapshot]>.Publisher { get }
}

// MARK: - OGSnapshotDistributor

final class OGSnapshotDistributor: SnapshotDistributable {
  @OGInjected(\OGDetectiveContainer.screenshotDetector) private var screenshotDetector

  var newSnapshot = PassthroughSubject<Snapshot, Never>()
  var snapshotsPublisher: Published<[Snapshot]>.Publisher { $snapshots }
  @Published private var snapshots: [Snapshot] = .init()

  private var cancellables: Set<AnyCancellable> = []

  init() {
    screenshotDetector.newScreenshot.sink { [weak self] screenshot in
      let snapshot = Snapshot(screenshot: screenshot)
      self?.snapshots.append(snapshot)
      self?.newSnapshot.send(snapshot)
    }.store(in: &cancellables)
  }
}
