import OGDIService
import OGNetworkLogger
import SwiftUI

// MARK: - SnapshotView

struct SnapshotView: View {
  @StateObject var viewModel: ViewModel

  @State private var showingNotesView = false
  @State private var sheetHeight: CGFloat = .zero
  @State private var showToast = false
  @State private var isPreparingSharingFiles = false

  var body: some View {
    ZStack {
      ScrollView {
        screenshotGroup
        ForEach(Array(viewModel.modules.enumerated()), id: \.element.identifier) { index, module in
          if index == 1 {
            snapshotNoteGroup
          }
          Group {
            snapshotView(forModule: module)
            scrollViewSpacer
          }
        }
      }
      .padding()
      .background(Theme.Token.Colors.background_10)
      .sheet(isPresented: $showingNotesView) {
        AnnotationsNotesView(note: viewModel.snapshot.note ?? "") {
          showingNotesView = false
        } closeAndSave: { text in
          viewModel.snapshot.note = text
          viewModel.objectWillChange.send()
          showingNotesView = false
          showToast.toggle()
        }
        .makeSheetView(sheetHeight: $sheetHeight)
      }
      .overlay {
        VStack {
          Spacer()
          if showToast {
            Toast(text: "Note has been added to Snapshot")
              .padding(.horizontal)
          }
        }
      }
    }
    .toolbar {
      if isPreparingSharingFiles {
        ProgressView()
      } else {
        Button {
          isPreparingSharingFiles.toggle()
          viewModel.shareSnapshots(snapshots: [viewModel.snapshot]) {
            isPreparingSharingFiles.toggle()
          }
        } label: {
          Theme.Token.Icons.share
            .renderingMode(.template)
            .foregroundColor(Theme.Token.Colors.system_blue)
        }
      }
    }
    .navigationTitle(viewModel.headlineDateString)
  }

  private var screenshotGroup: some View {
    Group {
      SnapshotScreenshotView(snapshot: $viewModel.snapshot)
      scrollViewSpacer
    }
  }

  private var snapshotNoteGroup: some View {
    Group {
      SnapshotNoteCard(text: viewModel.snapshot.note) {
        showingNotesView = true
      }
      scrollViewSpacer
    }
  }

  private var scrollViewSpacer: some View {
    Spacer()
      .frame(height: 16)
  }

  @ViewBuilder private var canvas: some View {
    ZStack {
      ForEach(viewModel.snapshot.lines) { line in
        Path { path in
          path.addLines(line.points)
        }
        .stroke(line.color, lineWidth: line.lineWidth)
      }
    }
    .border(.pink)
  }

  @ViewBuilder
  private func snapshotView(forModule module: any OGDetectiveModuleProtocol) -> AnyView {
    AnyView(module.snapshotView(forSnapshotDate: viewModel.snapshot.date))
  }
}

// MARK: SnapshotView.ViewModel

extension SnapshotView {
  final class ViewModel: ObservableObject {
    @Binding var snapshot: Snapshot

    @OGInjected(\OGDetectiveContainer.internalDetectiveService) private var internalDetectiveService

    var modules: [any OGDetectiveModuleProtocol] {
      internalDetectiveService.tabModules.values.flatMap { $0 }.filter { !($0 is SnapshotModule) }
    }

    var headlineDateString: String {
      let formatter = DateFormatter()
      formatter.dateFormat = "dd.MM.YYYY | HH:mm"
      return formatter.string(from: snapshot.date)
    }

    var lastFiveRequests: [OGNetworkLoggerRequestModel] {
      Array(snapshot.requests.prefix(5))
    }

    var lastFiveLogs: [OGDetectiveLog] {
      Array(snapshot.logs.prefix(5))
    }

    init(snapshot: Binding<Snapshot>) {
      self._snapshot = snapshot
    }

    func shareSnapshots(snapshots: [Snapshot], completion: @escaping () -> Void) {
      internalDetectiveService.shareSnapshots(snapshots: snapshots, completion: completion)
    }
  }
}

// MARK: - SnapshotView_Previews

struct SnapshotView_Previews: PreviewProvider {
  static let image = UIImage(named: "screenshot", in: .module, with: .none)!

  static var previews: some View {
    SnapshotView(
      viewModel: SnapshotView.ViewModel(
        snapshot: Binding(
          get: { Snapshot(
            screenshot: Screenshot(image: image, createdAt: Date())) },
          set: { _ in }
        )
      )
    )
  }
}
