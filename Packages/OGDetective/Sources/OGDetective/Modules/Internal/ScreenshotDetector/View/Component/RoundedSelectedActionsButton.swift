import SwiftUI

// MARK: - RoundedSelectedActionButton

struct RoundedSelectedActionButton<Content: View>: View {
  private var content: () -> Content
  private let size: CGFloat?
  private let action: () -> Void
  @Binding private var isSelected: Bool

  init(
    @ViewBuilder content: @escaping () -> Content,
    size: CGFloat? = nil,
    isSelected: Binding<Bool> = .constant(false),
    action:
    @escaping () -> Void
  ) {
    self.content = content
    self.size = size
    self.action = action
    self._isSelected = isSelected
  }

  var body: some View {
    Button {
      action()
      isSelected.toggle()
    } label: {
      content()
        .background(Theme.Token.Colors.background_15)
    }
    .highlighted(isSelected: isSelected, highlightedColor: Theme.Token.Colors.background_100)
    .background(Theme.Token.Colors.background_0)
    .if(size != nil, transform: { view in
      view.frame(width: size ?? 0, height: size ?? 0)
    })
    .clipShape(Circle())
  }
}

extension Button {
  func highlighted(isSelected: Bool, highlightedColor: Color) -> some View {
    buttonStyle(HighlightButtonStyle(isSelected: isSelected, highlightedColor: highlightedColor))
  }
}

// MARK: - HighlightButtonStyle

struct HighlightButtonStyle: ButtonStyle {
  let isSelected: Bool
  let highlightedColor: Color

  func makeBody(configuration: Configuration) -> some View {
    configuration.label
      .overlay(
        Circle()
          .stroke(highlightedColor, lineWidth: isSelected || configuration.isPressed ? 4 : 0)
      )
      .overlay(
        Circle()
          .stroke(Theme.Token.Colors.background_0, lineWidth: isSelected || configuration.isPressed ? 2 : 0)
          .padding(2)
      )
  }
}
