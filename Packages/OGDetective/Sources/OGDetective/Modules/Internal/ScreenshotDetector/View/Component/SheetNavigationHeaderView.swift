import SwiftUI

// MARK: - SheetNavigationHeaderView

struct SheetNavigationHeaderView: View {
  var title: String
  var action: () -> Void

  var body: some View {
    HStack(alignment: .center) {
      Spacer()
      Text(title)
        .fontWeight(.bold)
        .font(.system(size: 17))
      Spacer()
      RoundedSelectedActionButton(
        content: {
          Image(systemName: "xmark")
            .padding()
            .background(Theme.Token.Colors.background_10)
        },
        size: 30
      ) {
        action()
      }
    }
  }
}

// MARK: - SheetNavigationView_Previews

struct SheetNavigationView_Previews: PreviewProvider {
  static var previews: some View {
    SheetNavigationHeaderView(title: "Some title") {
      print()
    }
  }
}
