import SwiftUI

// MARK: - AnnotationsNotesView

struct AnnotationsNotesView: View {
  @State var note: String
  let close: () -> Void
  let closeAndSave: (_ text: String) -> Void

  @State private var showSaveButton: Bool = false

  var body: some View {
    VStack {
      SheetNavigationHeaderView(title: "Add notes") {
        close()
      }
      .padding()
      textEditor
      if showSaveButton {
        saveButton
      }
    }
  }

  @ViewBuilder var textEditor: some View {
    TextEditor(text: $note)
      .lineLimit(5)
      .frame(height: 120)
      .overlay(
        RoundedRectangle(cornerRadius: 14)
          .stroke(Theme.Token.Colors.background_15, lineWidth: 2)
      )
      .padding()
      .onReceive(NotificationCenter.default.publisher(for: UIResponder.keyboardWillShowNotification)) { _ in
        showSaveButton.toggle()
      }
  }

  @ViewBuilder var saveButton: some View {
    Button {
      closeAndSave(note)
    } label: {
      Text("Save note")
        .fontWeight(.bold)
        .frame(maxWidth: .infinity)
        .padding()
        .background(Theme.Token.Colors.system_blue)
        .foregroundColor(.white)
        .cornerRadius(14)
    }
    .padding()
  }
}

// MARK: - AnnotationsNotesView_Previews

struct AnnotationsNotesView_Previews: PreviewProvider {
  static var previews: some View {
    AnnotationsNotesView(note: "note") {
      print("close")
    } closeAndSave: { text in
      print("close with \(text)")
    }
  }
}
