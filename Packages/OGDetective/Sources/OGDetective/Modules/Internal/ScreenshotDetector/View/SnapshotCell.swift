import SwiftUI

// MARK: - SnapshotCell

struct SnapshotCell: View {
  let date: Date
  let image: Image

  @Binding var selectionEnabled: <PERSON><PERSON>
  @Binding var isSelected: Bool

  var formattedDate: String {
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = "dd.MM.yyyy"
    return dateFormatter.string(from: date)
  }

  var formattedTime: String {
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = "HH:mm"
    return dateFormatter.string(from: date)
  }

  var body: some View {
    VStack(spacing: .zero) {
      screenshot
      describingText
        .padding(.horizontal, 16)
        .padding(.vertical, 4)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color.white)
    }
    .background(Theme.Token.Colors.background_15)
    .cornerRadius(13)
    .overlay(
      RoundedRectangle(cornerRadius: 13)
        .stroke(Theme.Token.Colors.background_15, lineWidth: 1)
    )
  }

  var screenshot: some View {
    image
      .resizable()
      .scaledToFit()
      .frame(height: 144)
      .screenshotShadow()
      .padding()
  }

  var describingText: some View {
    HStack {
      VStack(alignment: .leading, spacing: .zero) {
        Text(formattedDate)
          .captionLBold()
          .foregroundColor(Theme.Token.Colors.text_dark_80)
        Text(formattedTime)
          .captionLRegular()
          .foregroundColor(Theme.Token.Colors.text_dark_80)
      }
      .foregroundColor(Theme.Token.Colors.background_15)
      Spacer()
      if isSelected {
        Theme.Token.Icons.checkmark
          .renderingMode(.template)
          .foregroundColor(Theme.Token.Colors.background_0)
          .background(
            Circle()
              .fill(Theme.Token.Colors.system_blue)
              .frame(width: 16, height: 16)
          )
      }
    }
  }
}

// MARK: - ScreenshotCell_Previews

struct ScreenshotCell_Previews: PreviewProvider {
  static var previews: some View {
    SnapshotCell(date: Date(), image: Image(packageResource: "screenshot", ofType: "png"), selectionEnabled: .constant(true), isSelected: .constant(true))
      .frame(width: 200)
  }
}
