import OGDIService
import SwiftUI

// MARK: - SnapshotGallery

struct SnapshotGallery: View {
  @StateObject private var viewModel = ViewModel()
  @State private var selectionEnabled = false
  @State private var selectedSnapshots = [Snapshot]()
  @State private var selectedSnapshot: Snapshot?
  @State private var isPreparingSharingFiles: Bool = false

  private let columns = [
    GridItem(.flexible()),
    GridItem(.flexible())
  ]

  var body: some View {
    ZStack {
      ScrollView {
        snapshotList
      }
      shareBar()
    }
    .toolbar {
      toolbarSelectionItems()
    }
    .navigationTitle("Gallery")
  }

  @ViewBuilder
  func shareBar() -> some View {
    if selectionEnabled {
      VStack {
        Spacer()
        VStack {
          if isPreparingSharingFiles {
            ProgressView()
          } else {
            HStack {
              Spacer()
              RoundedActionButton(
                content: {
                  Theme.Token.Icons.share
                    .renderingMode(.template)
                    .frame(width: 20, height: 20)
                },
                size: 40,
                isActive: !selectedSnapshots.isEmpty
              ) {
                isPreparingSharingFiles.toggle()
                viewModel.shareSnapshots(snapshots: selectedSnapshots) {
                  selectionEnabled = false
                  selectedSnapshots.removeAll()
                  isPreparingSharingFiles.toggle()
                }
              }
              .padding(.horizontal, 16)
              .padding(.top, 8)
            }
          }
        }
        .background(Theme.Token.Colors.background_0)
      }
    }
  }

  @ViewBuilder
  func toolbarSelectionItems() -> some View {
    Button {
      if selectionEnabled {
        selectionEnabled = false
        selectedSnapshots.removeAll()
      } else {
        selectionEnabled = true
      }
    } label: {
      Text(selectionEnabled ? "Cancel" : "Select")
        .bodyRegular()
        .foregroundColor(Theme.Token.Colors.system_blue)
    }
  }

  var snapshotList: some View {
    LazyVGrid(columns: columns, alignment: .center) {
      ForEach($viewModel.snapshots, id: \.self) { $snapshot in
        snapshotNavigationLink($snapshot)
      }
    }
  }

  @ViewBuilder
  private func snapshotNavigationLink(_ snapshot: Binding<Snapshot>) -> some View {
    let isActive = Binding<Bool>(
      get: { selectedSnapshot == snapshot.wrappedValue },
      set: { newValue in
        if newValue {
          selectedSnapshot = snapshot.wrappedValue
        } else {
          selectedSnapshot = nil
        }
      }
    )
    NavigationLink(
      destination: SnapshotView(viewModel: SnapshotView.ViewModel(snapshot: snapshot)),
      isActive: isActive,
      label: {
        SnapshotCell(
          date: snapshot.wrappedValue.date,
          image: Image(uiImage: snapshot.wrappedValue.thumbnail ?? snapshot.wrappedValue.image),
          selectionEnabled: $selectionEnabled,
          isSelected: .constant(selectedSnapshots.contains(snapshot.wrappedValue))
        )
        .onTapGesture {
          if selectionEnabled {
            if let index = selectedSnapshots.firstIndex(of: snapshot.wrappedValue) {
              selectedSnapshots.remove(at: index)
            } else {
              selectedSnapshots.append(snapshot.wrappedValue)
            }
          } else {
            isActive.wrappedValue = true
          }
        }
      }
    )
    .isDetailLink(false)
  }
}

// MARK: SnapshotGallery.ViewModel

extension SnapshotGallery {
  class ViewModel: ObservableObject {
    @OGInjected(\OGDetectiveContainer.internalDetectiveService) private var internalDetectiveService
    @OGInjected(\OGDetectiveContainer.snapshotDistributor) private var snapshotDistributor
    @Published var snapshots = [Snapshot]()

    init() {
      snapshotDistributor.snapshotsPublisher.assign(to: &$snapshots)
    }

    func shareSnapshots(snapshots: [Snapshot], completion: @escaping () -> Void) {
      internalDetectiveService.shareSnapshots(snapshots: snapshots, completion: completion)
    }
  }
}

// MARK: - SnapshotGallery_Previews

struct SnapshotGallery_Previews: PreviewProvider {
  static let image = UIImage(named: "screenshot", in: .module, with: .none)!
  static let screenshot = Screenshot(image: image, createdAt: Date())
  static let snapshots = [
    Snapshot(screenshot: screenshot),
    Snapshot(screenshot: screenshot),
    Snapshot(screenshot: screenshot),
    Snapshot(screenshot: screenshot),
    Snapshot(screenshot: screenshot),
    Snapshot(screenshot: screenshot),
    Snapshot(screenshot: screenshot),
    Snapshot(screenshot: screenshot)
  ]

  static var previews: some View {
    NavigationView {
      SnapshotGallery()
    }
  }
}
