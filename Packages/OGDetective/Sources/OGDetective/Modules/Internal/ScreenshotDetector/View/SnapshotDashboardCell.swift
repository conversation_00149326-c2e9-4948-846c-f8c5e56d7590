import SwiftUI

// MARK: - SnapshotDashboardCell

struct SnapshotDashboardCell: View {
  let image: Image
  let dateString: String

  var body: some View {
    ZStack {
      background
      Theme.Token.Colors.solid_30
      date
    }
    .frame(width: 52, height: 83)
    .cornerRadius(4)
  }

  private var background: some View {
    image
      .resizable()
      .aspectRatio(contentMode: .fit)
  }

  private var date: some View {
    VStack(alignment: .trailing) {
      Spacer()
      HStack {
        Spacer()
        Text(dateString)
          .font(.system(size: 11))
          .foregroundColor(.white)
          .padding(.horizontal, 6)
      }
    }
  }
}

// MARK: - SnapshotDashboardCell_Previews

struct SnapshotDashboardCell_Previews: PreviewProvider {
  static var previews: some View {
    SnapshotDashboardCell(image: Image(packageResource: "screenshot", ofType: "png"), dateString: "16:50")
      .padding()
  }
}
