import Combine
import OGDIService
import SwiftUI

// MARK: - AppInformationDashboardView

struct AppInformationDashboardView: View {
  @StateObject var viewModel = ViewModel()

  var body: some View {
    VStack(spacing: 16) {
      DeviceInfoCard(text: viewModel.deviceInfo)
      SystemMetricsCard(appInformationValues: viewModel.systemMetrics)
    }
  }
}

// MARK: - AppInformationView_Previews

struct AppInformationView_Previews: PreviewProvider {
  static var previews: some View {
    AppInformationDashboardView()
      .padding()
      .background(Color.gray)
  }
}

// MARK: - AppInformationDashboardView.ViewModel

extension AppInformationDashboardView {
  final class ViewModel: ObservableObject {
    @OGInjected(\OGDetectiveContainer.appInformationModule) var appInformationModule

    var deviceInfo: String {
      appInformationModule.appInformation.deviceInfoString
    }

    var systemMetrics: [AppInformationValue] {
      appInformationModule.appInformation.systemMetrics
    }

    @Published private var cpuFPS: String = ""
    @Published private var cpuUsage: String = ""
    @Published private var memoryFootprint: String = ""

    private var cancellables = Set<AnyCancellable>()

    init() {
      appInformationModule.cpuFPSPublisher.assign(to: &$cpuFPS)
      appInformationModule.memoryFootprintPublisher.assign(to: &$memoryFootprint)
      appInformationModule.cpuUsagePublisher.assign(to: &$cpuUsage)
    }
  }
}
