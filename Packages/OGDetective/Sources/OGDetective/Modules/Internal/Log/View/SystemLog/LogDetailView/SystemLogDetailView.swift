import OGCore
import OGLogger
import SwiftUI

// MARK: - SystemLogDetailView

struct SystemLogDetailView: View {
  var log: OGSystemLog?

  var body: some View {
    if let log {
      List {
        Section {
          keyValueCell(key: "Level:") {
            if let level = OGLogLevel(rawValue: log.tag ?? "") {
              LogLevelChip(level: level)
            }
          }

          keyValueCell(key: "Timestamp:", value: TimeInterval(log.timestamp).getDate())
        }
        Section {
          VStack(alignment: .leading, spacing: 4) {
            Text("Message")
              .titelSBold()
              .foregroundColor(Theme.Token.Colors.text_dark)
            Text(log.message)
              .bodyRegular()
              .foregroundColor(Theme.Token.Colors.text_dark_80)
          }
        }
        Section {
          if let fileName = log.fileName {
            keyValueCell(key: "File:", value: fileName)
          }

          if let methodName = log.methodName {
            keyValueCell(key: "Function:", value: methodName)
          }

          if let lineNumber = log.lineNumber {
            keyValueCell(key: "Line:", value: "\(lineNumber)")
          }
        }
      }
      .listStyle(InsetGroupedListStyle())
    } else {
      Text("No log found")
    }
  }

  @ViewBuilder
  private func keyValueCell(key: String, value: String) -> some View {
    keyValueCell(key: key) {
      Text(value)
        .bodyRegular()
        .foregroundColor(Theme.Token.Colors.text_dark_80)
    }
  }

  @ViewBuilder
  private func keyValueCell(key: String, @ViewBuilder content: () -> some View) -> some View {
    HStack {
      Text(key)
        .bodyRegular()
        .foregroundColor(Theme.Token.Colors.text_dark_80)
      Spacer()
      content()
    }
  }
}
