import OGLogger
import SwiftUI

// MARK: - LogCell

struct LogCell: View {
  private let log: OGLogEntry

  init(log: OGLogEntry) {
    self.log = log
  }

  var body: some View {
    HStack(spacing: 0) {
      IndicatorView(log: log)

      VStack(alignment: .leading) {
        Group {
          HStack {
            TimestampLabel(timeInterval: TimeInterval(log.timestamp))

            Spacer()

            StatusView(log: log)
          }
          .padding(.top, 8)

          TitleLabel(log: log)

          MessageLabel(log: log)
        }
        .padding(.horizontal, 8)

        Divider()
      }
    }
  }
}

// MARK: LogCell.TimestampLabel

extension LogCell {
  struct TimestampLabel: View {
    private let timeInterval: TimeInterval

    init(timeInterval: TimeInterval) {
      self.timeInterval = timeInterval
    }

    var body: some View {
      Text(timeInterval.getDate())
        .multilineTextAlignment(.leading)
        .captionSRegular()
        .foregroundStyle(Theme.Token.Colors.text_dark_80)
    }
  }
}

// MARK: LogCell.TitleLabel

extension LogCell {
  struct TitleLabel: View {
    private var title: String?

    init(log: OGLogEntry) {
      if let eventLog = log as? OGEventLog {
        self.title = eventLog.event.name
      } else if let networkLog = log as? OGNetworkLog {
        self.title = networkLog.method + " " + networkLog.url
      }
    }

    var body: some View {
      if let title {
        Text(title)
          .calloutBold()
          .multilineTextAlignment(.leading)
          .foregroundColor(Theme.Token.Colors.text_dark)
      }
    }
  }
}

// MARK: LogCell.MessageLabel

extension LogCell {
  struct MessageLabel: View {
    private var message: String?

    init(log: OGLogEntry) {
      if let eventLog = log as? OGEventLog {
        self.message = eventLog.getFormattedProperties()
      } else {
        self.message = log.message
      }
    }

    var body: some View {
      if let message {
        Text(message)
          .calloutRegular()
          .multilineTextAlignment(.leading)
          .foregroundColor(Theme.Token.Colors.text_dark)
      }
    }
  }
}

// MARK: LogCell.IndicatorView

extension LogCell {
  struct IndicatorView: View {
    private var color: Color

    init(log: OGLogEntry) {
      self.color = log.color
    }

    var body: some View {
      Rectangle()
        .foregroundStyle(color)
        .frame(width: 8)
    }
  }
}

// MARK: LogCell.StatusView

extension LogCell {
  struct StatusView: View {
    private let color: Color
    private var title: String?

    init(log: OGLogEntry) {
      self.color = log.color
      self.title = getTitle(for: log)
    }

    var body: some View {
      if let title {
        Text(title.uppercased())
          .foregroundStyle(Theme.Token.Colors.text_light)
          .captionSRegular()
          .padding(.horizontal, 8)
          .background(
            RoundedRectangle(cornerRadius: 8)
              .foregroundStyle(color)
              .frame(height: 16)
          )
          .frame(height: 16)
      }
    }

    private func getTitle(for log: OGLogEntry) -> String? {
      if let networkLog = log as? OGNetworkLog {
        return "\(networkLog.httpStatus)"
      }

      if let systemLog = log as? OGSystemLog {
        return systemLog.tag
      }

      return nil
    }
  }
}
