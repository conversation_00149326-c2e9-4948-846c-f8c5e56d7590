import SwiftUI
import WebKit

// MARK: - DataDetailView

struct DataDetailView: View {
  let navigationTitle: String
  @State var searchText = ""
  @StateObject private var webViewManager: WebViewManager

  init(code: String, navigationTitle: String) {
    _webViewManager = StateObject(wrappedValue: WebViewManager(htmlString: code))
    self.navigationTitle = navigationTitle
  }

  var body: some View {
    VStack(spacing: .zero) {
      OGDetectiveSearchBar(searchText: $searchText)
        .padding()
      searchNavigation()
        .padding(.horizontal)
      WebView(webViewManager: webViewManager)
        .scaledToFit()
        .background(Theme.Token.Colors.background_0)
        .cornerRadius(12)
        .padding()
        .onChange(of: searchText) { _ in
          webViewManager.searchForText(searchText)
        }
      Spacer()
    }
    .background(Theme.Token.Colors.background_10)
    .navigationTitle(navigationTitle)
  }

  @ViewBuilder
  func searchNavigation() -> some View {
    if !searchText.isEmpty {
      HStack(spacing: .zero) {
        Text("\(webViewManager.occurrences) results")
          .captionSRegular()
          .multilineTextAlignment(.leading)
          .frame(maxWidth: .infinity, alignment: .leading)
        Button {
          webViewManager.searchPreviousSelector()
        } label: {
          Theme.Token.Icons.chevronUp
            .foregroundColor(Theme.Token.Colors.text_dark_80)
        }
        Spacer()
          .frame(width: 16)
        Button {
          webViewManager.searchNextSelector()
        } label: {
          Theme.Token.Icons.chevronDown
            .foregroundColor(Theme.Token.Colors.text_dark_80)
        }
      }
      .frame(maxWidth: .infinity, minHeight: 16)
    } else {
      Spacer()
        .frame(height: 16)
    }
  }
}

// MARK: - DataDetailView_Previews

struct DataDetailView_Previews: PreviewProvider {
  static let json = """
    {
      "name": "John DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn Doe",
      "age": 30,
      "city": null
    }
  """

  static var previews: some View {
    DataDetailView(code: json, navigationTitle: "Some Body")
  }
}
