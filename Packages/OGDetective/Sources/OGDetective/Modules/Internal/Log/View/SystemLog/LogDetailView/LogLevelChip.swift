import OGCore
import SwiftUI

// MARK: - LogLevelChip

struct LogLevelChip: View {
  let level: OGLogLevel

  var body: some View {
    HStack(spacing: .zero) {
      logDot
      logLevelTitle
    }
  }

  private var logDot: some View {
    colorForLevel()
      .clipShape(Circle())
      .frame(width: 6, height: 6)
  }

  private var logLevelTitle: some View {
    Text(" \(level.rawValue.capitalized)")
      .footnoteRegular()
      .foregroundColor(Theme.Token.Colors.text_dark_80)
  }

  private func colorForLevel() -> Color {
    switch level {
    case .debug:
      return Theme.Token.Colors.yellow_log
    case .warning:
      return Theme.Token.Colors.orange_log
    case .critical:
      return Theme.Token.Colors.red_log
    }
  }
}

// MARK: - LogLevelChip_Previews

struct LogLevelChip_Previews: PreviewProvider {
  static var previews: some View {
    Group {
      LogLevelChip(level: .critical)
        .previewLayout(PreviewLayout.sizeThatFits)
        .padding()
      LogLevelChip(level: .debug)
        .previewLayout(PreviewLayout.sizeThatFits)
        .padding()
      LogLevelChip(level: .warning)
        .previewLayout(PreviewLayout.sizeThatFits)
        .padding()
    }
  }
}
