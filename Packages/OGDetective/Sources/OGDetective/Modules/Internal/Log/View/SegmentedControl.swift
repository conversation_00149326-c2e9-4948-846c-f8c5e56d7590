import SwiftUI

// MARK: - SegmentedControl

@available(iOS 15.0, *)
struct SegmentedControl<T: RawRepresentable & Hashable>: View where T.RawValue == String {
  @Binding var selectedElement: T
  let categories: [T]
  @Namespace private var animation

  var body: some View {
    HStack(spacing: .zero) {
      ForEach(categories, id: \.self) { category in
        Button {
          withAnimation {
            selectedElement = category
          }
        } label: {
          VStack(spacing: .zero) {
            Text(category.rawValue)
              .captionLBold()
              .foregroundColor(selectedElement == category ? Theme.Token.Colors.text_dark : Theme.Token.Colors.text_dark_80)
              .frame(maxWidth: .infinity, alignment: .center)
            Color.clear
              .frame(height: 6)
              .matchedGeometryEffect(id: category.rawValue, in: animation, isSource: true)
          }
          .background(.clear)
        }
      }
      .overlay {
        Rectangle()
          .fill(Theme.Token.Colors.background_100)
          .frame(height: 2)
          .matchedGeometryEffect(id: selectedElement.rawValue, in: animation, isSource: false)
      }
    }
    .frame(maxWidth: .infinity)
    .background(.clear)
  }
}
