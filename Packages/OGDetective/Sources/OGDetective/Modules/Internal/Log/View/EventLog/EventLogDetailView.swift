import OGCore
import SwiftUI

// MARK: - EventLogDetailView

struct EventLogDetailView: View {
  var log: OGEventLog?

  var body: some View {
    if let log {
      List {
        Section {
          VStack(alignment: .leading, spacing: 4) {
            Text(log.message)
              .bodyRegular()
              .foregroundColor(Theme.Token.Colors.text_dark_80)
          }

          keyValueCell(key: "Timestamp", value: TimeInterval(log.timestamp).getDate())
        }

        Section {
          VStack(alignment: .leading, spacing: 4) {
            Text("Parameters")
              .titelSBold()
              .foregroundColor(Theme.Token.Colors.text_dark)

            Text(log.getFormattedProperties() ?? "")
              .bodyRegular()
              .foregroundColor(Theme.Token.Colors.text_dark_80)
          }
        }

        Section {
          VStack(alignment: .leading, spacing: 4) {
            Text("Tracking services")
              .titelSBold()
              .foregroundColor(Theme.Token.Colors.text_dark)

            ForEach(log.trackingServices, id: \.self) { service in
              keyValueCell(key: service.serviceName, value: "\(service)")
            }
          }
        }
      }
      .listStyle(InsetGroupedListStyle())
      .navigationTitle(log.event.name)
    } else {
      Text("No log found")
    }
  }

  @ViewBuilder
  private func keyValueCell(key: String, value: String) -> some View {
    keyValueCell(key: key) {
      Text(value)
        .bodyRegular()
        .foregroundColor(Theme.Token.Colors.text_dark_80)
    }
  }

  @ViewBuilder
  private func keyValueCell(key: String, @ViewBuilder content: () -> some View) -> some View {
    HStack {
      Text(key)
        .bodyRegular()
        .foregroundColor(Theme.Token.Colors.text_dark_80)
      Spacer()
      content()
    }
  }
}
