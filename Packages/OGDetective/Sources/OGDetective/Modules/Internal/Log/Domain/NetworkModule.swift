import Combine
import OGDIService
import OGNetworkLogger
import SwiftUI

// MARK: - NetworkModuleProtocol

protocol NetworkModuleProtocol: ObservableObject, OGDetectiveModuleProtocol {
  var requestsPublisher: Published<[OGNetworkLoggerRequestModel]>.Publisher { get }
}

// MARK: - NetworkModule

final class NetworkModule: NetworkModuleProtocol {
  public var id = UUID()
  public var identifier: UUID { id }
  var exportFileName = "Network"

  @OGInjected(\OGNetworkLoggerContainer.networkLogger) var networkLogger

  var requestsPublisher: Published<[OGNetworkLoggerRequestModel]>.Publisher { $requests }
  private var cancellables = Set<AnyCancellable>()

  @Published private var requests: [OGNetworkLoggerRequestModel] = .init()

  init() {
    listenToLogger()
  }

  private func listenToLogger() {
    networkLogger.requestPublisher
      .receive(on: DispatchQueue.main)
      .sink { [weak self] requests in
        self?.requests = requests
      }.store(in: &cancellables)
  }

  private var snapshots: [Date: [OGNetworkLoggerRequestModel]] = [:]

  func exportData(forDate date: Date) -> String? {
    guard let snapshotRequests = snapshots[date] else { return nil }
    let separator = """

    ---------------------------------------------

    """
    return snapshotRequests.map(\.exportString).joined(separator: separator)
  }

  func didTakeSnapshot(atDate date: Date) {
    snapshots[date] = requests
  }

  func dashboardView() -> some View {
    dashboardViewWrapper()
  }

  @ViewBuilder
  private func dashboardViewWrapper() -> some View {
    EmptyView()
  }

  func snapshotView(forSnapshotDate date: Date) -> some View {
    snapshotViewWrapper(date: date)
  }

  @ViewBuilder
  private func snapshotViewWrapper(date _: Date) -> some View {
    EmptyView()
  }
}
