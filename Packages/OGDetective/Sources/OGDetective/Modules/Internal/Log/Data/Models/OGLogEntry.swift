import Foundation
import OGAppKitSDK
import SwiftUI

public typealias OGEventLog = EventLog
public typealias OGNetworkLog = NetLog
public typealias OGSystemLog = SysLog
public typealias OGBaseLog = BaseLog
public typealias OGCustomLog = CustomLog

public typealias OGLogEntry = LogEntry & Sendable

extension LogEntry {
  public var id: String {
    if let eventLog = self as? OGEventLog {
      return eventLog.event.name + ":" + String(eventLog.timestamp)
    }

    if let networkLog = self as? OGNetworkLog {
      return networkLog.url + ":" + String(networkLog.timestamp)
    }

    return message + ":" + String(timestamp)
  }

  public var color: Color {
    if let eventLog = self as? OGEventLog {
      return eventLog.getColor()
    }

    if let networkLog = self as? OGNetworkLog {
      return networkLog.getColor()
    }

    if let systemLog = self as? OGSystemLog {
      return systemLog.getColor()
    }

    return Theme.Token.Colors.text_light
  }
}
