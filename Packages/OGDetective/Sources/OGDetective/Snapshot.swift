import Combine
import OGDIService
import OGNetworkLogger
import SwiftUI

// MARK: - Snapshot

final class Snapshot: Identifiable {
  @OGInjected(\OGDetectiveContainer.appInformationModule) private var appInformationModule
  @OGInjected(\OGDetectiveContainer.networkModule) private var networkModule
  @OGInjected(\OGDetectiveContainer.logModule) private var logModule

  let id: UUID = .init()
  let date: Date
  let image: UIImage

  var thumbnail: UIImage?
  var note: String?
  var lines: [AnnotationsLine] = .init()

  var deviceInfo: String = .init()
  var systemMetrics: [AppInformationValue] = []

  var requests: [OGNetworkLoggerRequestModel] = .init()
  var logs: [OGDetectiveLog] = .init()

  private var cancellables = Set<AnyCancellable>()

  init(screenshot: Screenshot, lines: [AnnotationsLine] = .init()) {
    self.date = screenshot.createdAt
    self.image = screenshot.image
    self.lines = lines

    self.deviceInfo = appInformationModule.appInformation.deviceInfoString
    self.systemMetrics = appInformationModule.appInformation.systemMetrics

    let requestsPublisher = networkModule.requestsPublisher.first()
    let logsPublisher = logModule.logsPublisher.first()

    Publishers.Zip(requestsPublisher, logsPublisher)
      .sink { [weak self] requests, logs in
        guard let self else { return }
        self.requests = requests
        self.logs = logs
        cancellables.removeAll()
      }
      .store(in: &cancellables)
  }

  func drawOnImage(sizeOfCanvas: CGSize) {
    UIGraphicsBeginImageContext(image.size)
    image.draw(at: CGPoint.zero)
    guard let context = UIGraphicsGetCurrentContext() else { return }

    for line in lines {
      context.setStrokeColor(line.color.cgColor ?? CGColor(red: 0, green: 0, blue: 0, alpha: 1))
      context.setLineWidth(line.lineWidth * UIScreen.main.scale)

      let path = CGMutablePath()

      path.addLines(between: line.points.map { CGPoint(x: $0.x * (image.size.width / sizeOfCanvas.width), y: $0.y * (image.size.height / sizeOfCanvas.height)) })
      context.addPath(path)
      context.strokePath()
    }

    let myImage = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()

    thumbnail = myImage
  }
}

// MARK: Hashable

extension Snapshot: Hashable {
  func hash(into hasher: inout Hasher) {
    hasher.combine(id)
    hasher.combine(date)
  }

  static func == (lhs: Snapshot, rhs: Snapshot) -> Bool {
    lhs.id == rhs.id &&
      lhs.date == rhs.date &&
      lhs.image == rhs.image &&
      lhs.note == rhs.note &&
      lhs.requests == rhs.requests &&
      lhs.logs == rhs.logs
  }
}
