# ``OGDetective``

The `OGDetective` is a user-friendly debugging tool designed to provide valuable insights into the inner workings of your application. It enables logging and persistent storage of all data throughout the current session. It's important to note that once the app is closed, all data will be automatically removed.

The dashboard offers several built-in modules, including Device Information, System Metrics, Snapshots, Network, and Logs. Additionally, `OGDetective` supports the ability to seamlessly integrate your own custom modules for further customization and flexibility.

> ⚠️ **If you want to log requests in `WKWebViews` take a look at [WebView Logging](#webview-logging)**


## Integration

Integrating the `OGDetective` framework into your application is designed to be a straightforward process.  Simply inject the detective via the `OGDIService`. This approach guarantees that every relevant event and action will be logged for further analysis and debugging purposes.

```swift 
import OGDetective
import OGDIService 

final class AppDelegate: NSObject, UIApplicationDelegate {
  @OGInjected(\OGDetectiveContainer.detective) private var detective
  
  ...
}
```

To display the `OGDetective` dashboard and its subviews, use the `SwiftUI` `View` called `OGDetectiveView()`.

Here's an example of how you can call and integrate this view into your code:

```swift
struct ContentView: View {
    @State private var showOGDetective = false
    
    var body: some View {
        Button("Show OGDetective") {
            showOGDetective = true
        }
        .sheet(isPresented: $showOGDetective) {
            OGDetectiveView()
        }
    }
}
```


## Customization 

### Create Your Own Modules

If you find the need to add your own custom module within `OGDetective`, you have the flexibility to integrate it seamlessly. Your custom module will be displayed alongside the other built-in modules in both the dashboard and snapshot detail view. Additionally, your custom module can also provide the ability to export its own data.

By leveraging this integration capability, you can extend the functionality of `OGDetective` to suit your specific requirements. This also allows you to export data from your custom module.

> Remember, if any other teams could benefit from your custom module, please open a PR to share it with everyone.

#### Create a Module

Create a class that represents your module and that conforms to the `OGDetectiveModuleProtocol`. 

```swift
class CustomModule: OGDetectiveModuleProtocol {
  typealias T = AnyView
  
  var id: UUID = UUID()
  
  var snapshotData = [Date: String]()
  
  
  init() {}
  
  func exportData(forDate date: Date) -> String? {
    guard let data = snapshotData.first(where: { $0.key == date})?.value else { return "Error creating exportable data" }
    
    return data
  }
  
  func didTakeSnapshot(atDate date: Date) {
    let formatter = DateFormatter()
    formatter.dateFormat = "dd.MM.YYYY HH:mm"
    let formattedDate = formatter.string(from: date)
    snapshotData[date] = "Snapshot was triggered at \(formattedDate)"
  }
  
  func dashboardView() -> T {
    AnyView(Text("My custom module")
      .foregroundColor(.blue))
  }
  
  func snapshotView(forSnapshotDate date: Date) -> T {
    AnyView(Text("My custom module")
      .foregroundColor(.red))
  }
}
```

#### Use custom components

If you are in need of adding a custom module to the `OGDetective` panel, you can also benefit from the already made views. There are four elements that you could make use of instead of having to code them. Let's have a look.

- Switch
- Button
- Text
- Text and navigation
- Key-value pairs

##### Switch
If you need to activate/deactivate a feature, this is your component of choice. The component you will use is `HeadlineWithSwitch`. The component will look like this:
@Image(source: "headline_with_switch.jpg")

##### Button
At some point you may need to just trigger an action, so this is your choice! On that case, you should use the `HeadlineWithButton` element.
@Image(source: "headline_with_button.jpg", alt: "An example of a headline and a button.")

##### Text
Just to show some text, or maybe display a navigatable section, you can use the component `HeadlineWithText`. When providing an element to navigate to, it will show the chevron.
@Image(source: "headline_with_text_and_navigation.jpg", alt: "An example of a headline text that you can navigate into.")
When there are no underlying elements, it shows just the text.
@Image(source: "headline_with_text.jpg", alt: "An example of a headline text and no more.")

##### Key-value pairs
Sometimes all you need is to show a list of key-value pairs, like the contents of a dictionary. On that case, the component `HeadlineWithKeyValuePairs`.
@Image(source: "headline_with_key_value_pairs.jpg", alt: "An example of a headline and a bunch of key-value pairs.")

Now that we have seen these four components that we could utilize, the way of using them is integrating into a module, as shown in [Create your own modules](#create-your-own-modules). Also, feel free to take a look on the `OGKitDemoApp`, into the Detective part there is examples for all of these elements we just talked about.

#### Register Module

Register your module at the start of your application. If you add your own module, there is no need to invoke the `OGDetective` beforehand. This will append your custom module at the bottom of the dashboard and the snapshot view.

```swift 

@OGInjected(\OGDetectiveContainer.detective) private var detective

func application(_: UIApplication, didFinishLaunchingWithOptions _: [UIApplication.LaunchOptionsKey: Any]? = nil) -> Bool {

    detective.setModuleOrder([
      .appInformation: [
        OGDetective.DefaultModules.appInformation
      ]
    ])

    return true
}
```

### Custom Order of Modules

To customize the order of modules within each specified tab, you can utilize the `setModuleOrder(_ moduleOrder: [TabType: [CustomModuleOrderProtocol]])` function. To append modules to an existing tab, you can use the func `appendModulesToTab(_ modules: [TabType: [any OGDetectiveModuleProtocol]])`. To replace an existing tab with new modules or adds a new tab if it doesn't already exist, you can use `func addTabWithModules(_ tabModules: [TabType: [any OGDetectiveModuleProtocol]])`. Within the `OGDetective` class, there exists an enum named `DefaultModules` that offers a convenient way to include the default modules.

In the following example, we demonstrate how to modify the order of modules. Keep in mind that the visibility of the `OGDetective.DefaultModules.snapshot` module depends on whether a screenshot has been captured.



```swift
@OGInjected(\OGDetectiveContainer.detective) private var detective

func application(_: UIApplication, didFinishLaunchingWithOptions _: [UIApplication.LaunchOptionsKey: Any]? = nil) -> Bool {

    detective.setModuleOrder([
      .appInformation: [
        OGDetective.DefaultModules.appInformation
      ],
      .snapshot: [OGDetective.DefaultModules.snapshot],
      .logs: [
        OGDetective.DefaultModules.log,
        OGDetective.DefaultModules.network
      ]
    ])

    detective.appendModulesToTab([
      .appInformation: [
        HeadlineWithSwitchModule(),
        HeadlineWithButtonModule(),
        HeadlineWithTextModule(),
        HeadlineWithTextAndContentModule(),
        HeadlineWithKeyValuePairsAndContentModule()
      ]
    ])
    return true
 }
```

## Default Modules

### Device Information

The Device Information module within OGDetective offers valuable insights into various static information about the device on which the application is running. 

This module provides the following details:

- App name
- OS version
- Device model
- Device language
- Storage capacity 
- App Version 
- Build number 
- Screen size

### System Metrics 

The System Metrics module in OGDetective offers real-time data regarding key performance metrics of the device. It provides valuable insights into:

**Frames Per Second (FPS)**: This metric measures the number of frames rendered per second, indicating the smoothness of the application's animations and visuals.

**Central Processing Unit (CPU) Usage**: This metric tracks the current CPU utilization, giving you an understanding of how much processing power your application is consuming.

**Memory Usage**: This metric provides information about the current memory utilization, allowing you to monitor the memory footprint of your application in real-time.

### Snapshots

The Snapshots module in OGDetective is triggered by capturing a screenshot. This module is only visible if a snapshot has been triggered or created. A snapshot captures the state and history of the running application, preserving all the information collected by the modules at the specific moment in time.

With the Snapshot feature, you gain the ability to inspect all the available data captured during the snapshot. Additionally, you can annotate the screenshot itself by drawing on it, allowing you to highlight and mark crucial findings directly on the visual representation.
@Image(source: "snapshot.png", alt: "An example of how you could scribble onto a snapshot.")


### Network

The Network module in OGDetective offers comprehensive insights into network requests made by URLSessions. This module automatically logs all requests fired by URLSessions, providing valuable information for analysis and debugging.

Within the UI of the Network module, you have access to various details, including:

Request and Response Header Fields: You can view the header fields of both the request and response, providing insight into the metadata exchanged during the network communication.

Request and Response Bodies: The module allows you to examine the bodies of both the request and response, providing visibility into the actual content transmitted.

Additional Duration Information: You can also access additional information like the duration of the network requests, giving you insights into the timing and performance aspects.

#### WebView Logging

To enable logging of network requests made by a WKWebView, `OGDetective` provides a service class called `OGWebViewLoggingService`. This service class allows you to capture and log requests made by the `WKWebView` by utilizing the `OGNetworkLogger`.

To understand how to use the `OGWebViewLoggingService`, you can refer to the documentation for the `OGNetworkLogger` framework. 
The documentation, that can be found at, from the root folder, Packages/OGNetworkLogger/Sources/OGNetworkLogger/Documentation.docc/Documentation.md provides detailed information on how to integrate and utilize the `OGNetworkLogger` for logging network requests.

### Log

The Log module in `OGDetective` provides a comprehensive display of all logs received through the `OGLogger`. This module presents all the available information associated with each log entry.

- Log Content: The actual content or message of the log entry.
- Log Level: The severity or level of the log, such as "debug", "warning", or "critical."
- Timestamp: The date and time when the log was generated.
- Additional Context: Any additional contextual information that may have been included with the log.

### Exporting

In the Snapshot Gallery and Snapshot Detail view of `OGDetective`, you have access to an exporting function. This functionality allows you to generate a Zip folder that contains selected snapshots along with their associated data.

When exporting, each module that provides exportable data is represented in its own file within the Zip folder. This ensures that the data from different modules remains organized and easily accessible.
