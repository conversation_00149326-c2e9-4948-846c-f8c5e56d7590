import OGDIService

@available(iOS 15.0, *)
public final class OGDetectiveContainer: OGDISharedContainer {
  public static var shared: OGDetectiveContainer = .init()

  public var manager: OGDIContainerManager = .init()

  public var detective: OGDIService<any OGDetectiveProtocol> {
    self {
      OGDetective()
    }.cached
  }

  internal var internalDetectiveService: OGDIService<any InternalDetectiveServicable> {
    self {
      InternalDetectiveService()
    }.cached
  }

  internal var logModule: OGDIService<any LogModuleProtocol> {
    self {
      LogModule()
    }.cached
  }

  internal var networkModule: OGDIService<any NetworkModuleProtocol> {
    self {
      NetworkModule()
    }.cached
  }

  internal var eventModule: OGDIService<any EventLogsModuleProtocol> {
    self {
      EventLogsModule()
    }.cached
  }

  internal var appInformationModule: OGDIService<any AppInformationModuleProtocol> {
    self {
      AppInformationModule()
    }.cached
  }

  internal var snapshotModule: OGDIService<any SnapshotModuleProtocol> {
    self {
      SnapshotModule()
    }.cached
  }

  internal var screenshotDetector: OGDIService<any OGScreenshotDetectable> {
    self {
      OGScreenshotDetector()
    }.cached
  }

  internal var snapshotDistributor: OGDIService<any SnapshotDistributable> {
    self {
      OGSnapshotDistributor()
    }.cached
  }
}
