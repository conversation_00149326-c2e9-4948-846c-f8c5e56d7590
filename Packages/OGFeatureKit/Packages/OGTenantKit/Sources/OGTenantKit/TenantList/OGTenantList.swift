import Foundation
import OGTenantCore

// MARK: - OGTenantList

/// This struct represents a list of tenants.
/// ```json
/// {
/// "displayName": "Deutschland",
/// "isDefault": true,
/// "supportedLocales": ["de-DE"],
/// "features": []
/// }
/// ```
public struct OGTenantList {
  /// The tenants in the list.
  public let tenants: [OGTenant]
}

extension OGTenantList {
  /// An empty tenant list.
  public static let empty = OGTenantList(tenants: [])
}

// MARK: Codable

extension OGTenantList: Codable {}
