import Foundation
import OGIdentifier
import OGTenantCore

// MARK: - OGTenantProvidable

///  This protocol defines a method for retrieving a tenant by its identifier.
public protocol OGTenantProvidable {
  /// Retrieves the tenant with the specified identifier.
  /// - Parameters:
  /// - identifier: The identifier of the tenant to retrieve.
  /// - Returns: The tenant with the specified identifier.
  /// - Throws: An error if a tenant with the specified identifier is not found.
  func tenant(for identifier: OGIdentifier) throws -> OGTenant
}

// MARK: - TenantSelectionProvidable

/// This protocol defines a property for the currently selected tenant.
public protocol TenantSelectionProvidable {
  /// The currently selected tenant.
  var tenant: OGTenant? { get }
}

// MARK: - TenantsProvidable

/// This  protocol defines a property for all available tenants.
public protocol TenantsProvidable {
  /// All available tenants.
  var tenants: [OGTenant] { get }
}
