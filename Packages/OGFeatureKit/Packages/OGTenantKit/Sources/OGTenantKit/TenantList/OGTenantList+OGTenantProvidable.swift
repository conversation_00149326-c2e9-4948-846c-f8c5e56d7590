import Foundation
import OGIdentifier
import OGTenantCore

extension OGTenantList: OGTenantProvidable {
  /// Gets the tenant with the given identifier.
  /// - Parameter identifier: The identifier of the tenant to get.
  /// - Throws: An error if no tenant with the given identifier exists.
  /// - Returns: The tenant with the given identifier.
  public func tenant(for identifier: OGIdentifier) throws -> OGTenant {
    try tenants.element(for: identifier)
  }
}
