@Tutorial(time: 30) {
		@Intro(title: "Managing Tenants") {
				In this tutorial, you will learn how to setup a tenant JSON, fetch a list of tenants, select a tenant, and listen for tenant changes.
				@Image(source: OGKit.png, alt: "")
		}
		
		
		@Section(title: "Creating the Tenant JSON") {
				@ContentAndMedia {
						Setup a JSON of a Tenant.
						@Image(source: OGKit.png, alt: "")
				}
				
				@Steps {
						@Step {
								Create a new JSON file named `de-DE.json` and add it as a target dependency to your project. The file name represents a specific language and region (eg. de-DE), or just a language (eg. de), or just a region (eg. DE).
								
								@Code(name: "de-DE.json", file: tenants_0.json) 
						}    
						
						@Step {
								Add a property called `displayName` to the object. Thats the name of the tenant written in the tenants language (e.g. Deutschland).
								
								@Code(name: "de-DE.json", file: tenants_1.json) 
						}
						
						@Step {
								if Germany is your default language, add the `isDefault` flag to this object.note: An object must have the `isDefault` flag if there is more than one tenant.
								
								@Code(name: "de-DE.json", file: tenants_2.json)
						}
						
						@Step {
								Add an array called `supportedLocales` to the object. This array contains all the regions that are supported by this tenant.
								
								@Code(name: "de-DE.json", file: tenants_3.json)
						}   
						
						@Step {
								For all german regions just use the upper case region identifier (`DE`). For all german languages just use the lower case language identifier `de`. Or if you just want specific ones use `de-DE, de-AT`. The first entry in the `supportedLocales` needs to match the filename.
								
								@Code(name: "de-DE.json", file: tenants_4.json)
						}  
						
						@Step {
								Add an array called `feature` and leave it empty for now. You can learn more about features in the OGFeatureKit tutorial.
								
								@Code(name: "de-DE.json", file: tenants_5.json) 
						}    
						
						@Step {
								Continue adding more files as a target dependency to your project. As mentions above, the file name represents a specific language and region (.e.g `fr-FR.json`, `nl-NL.json`)
								
								@Code(name: "fr-FR.json", file: tenants_6.json) 
						}
				}
		}
		
		@Section(title: "Display a List of all Tenants.") {
				@ContentAndMedia {
						Display a list of all available Tenants in your App.
						
						@Image(source: OGKit.png, alt: "")
				}
				
				@Steps {
						@Step {
								Create a file named `TenantsListView+ViewModel.swift` and add it as a target dependency to your project.
								@Code(name: "TenantsListView+ViewModel.swift", file: TenantListView+Model_01.swift) 
						} 
						
						@Step {
								import the `FeatureKit` and the `OGDIService` Module.
								@Code(name: "TenantsListView+ViewModel.swift", file: TenantListView+Model_02.swift) 
						} 
						
						@Step {
							Inject the TenantKit `service` defined in the OGTenantContainer
							@Code(name: "TenantsListView+ViewModel.swift", file: TenantListView+Model_03.swift)
					
						} 
						@Step {
							Load the a list of tenants via `service.tenants`   
							@Code(name: "TenantsListView+ViewModel.swift", file: TenantListView+Model_04.swift)
						}
				}
		}
		@Section(title: "Selecting a Tenant.") {
				@ContentAndMedia {
						Learn how to set the users selected Tenant.
						
						@Image(source: OGKit.png, alt: "")
				}
				
				@Steps {
						@Step {
							Continue here.
							@Code(name: "TenantsListView+ViewModel.swift", file: TenantListView+Model_04.swift)
						} 
						@Step {
							Set a selected tenant via `try selectionService.setTenant(for identifier: OGIdentifier)`   
							@Code(name: "TenantsListView+ViewModel.swift", file: TenantListView+Model_05.swift)
							
						} 
						@Step {
							update the `onFetchTenants` function and set the isSelected flag via `tenantService.selectedTenant`
							@Code(name: "TenantsListView+ViewModel.swift", file: TenantListView+Model_06.swift)
					
						} 
				}
		}
		@Section(title: "Listening to Tenant changes.") {
				@ContentAndMedia {
						Learn how to listen to Tenant changes.
						
						@Image(source: OGKit.png, alt: "")
				}
				
				@Steps {
						@Step {
							Continue here.
							@Code(name: "TenantsListView+ViewModel.swift", file: TenantListView+Model_06.swift)
						} 
						@Step {
							Import Combine.
							@Code(name: "TenantsListView+ViewModel.swift", file: TenantListView+Model_07.swift)
							
						} 
						@Step {
							Add the subscriptions property.
							@Code(name: "TenantsListView+ViewModel.swift", file: TenantListView+Model_08.swift)
					
						} 
						@Step {
							Listen to selected tenants via `tenantService.selectedTenantPublished`
							@Code(name: "TenantsListView+ViewModel.swift", file: TenantListView+Model_09.swift)
					
						}
						@Step {
							Create a new function that takes the selected tenant and updates the tenants list.  
							@Code(name: "TenantsListView+ViewModel.swift", file: TenantListView+Model_10.swift)
					
						} 
				}
		}
}
