import Foundation
import OGDIService
import OGFeatureKit
import OGIdentifier

extension TenantListView {
  class ViewModel: ObservableObject {
    struct Model: Identifiable {
      public let id: String
      public let name: String
      public let isSelected: Bool
    }

    enum Action {
      case selectTenant(Model)
      case fetchTenants
    }

    @Published var tenants = [Model]()
    @OGInjected(\OGTenantContainer.service) private var tenantService
  }
}
