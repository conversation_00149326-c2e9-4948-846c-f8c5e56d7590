import Foundation
import OGDIService
import OGFeatureKit
import OGIdentifier

extension TenantListView {
  class ViewModel: ObservableObject {
    struct Model: Identifiable {
      public let id: String
      public let name: String
      public let isSelected: Bool
    }

    enum Action {
      case selectTenant(Model)
      case fetchTenants
    }

    @Published var tenants = [Model]()
    @OGInjected(\OGTenantContainer.service) private var tenantService

    private func onFetchTenants() {
      tenants = tenantService.tenants.map {
        Model(
          id: $0.identifier.value,
          name: $0.localeSpecificName.value,
          isSelected: $0.identifier.value == tenantService.selectedTenant?.identifier.value ?? ""
        )
      }
    }

    private func onTenantSelect(_ tenant: Model) {
      try? tenantService.setTenant(for: #identifier(tenant.id))
    }
  }
}
