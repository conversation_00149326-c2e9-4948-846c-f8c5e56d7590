import Combine
import Foundation
import OGDIService
import OGTenantCore

/// This class observes tenant selection.
public class OGTenantObserver: OGTenantObservable {
  @Published private var tenant: OGTenant?

  /// A publisher for tenant selection updates.
  public var selectedTenant: Published<OGTenant?>.Publisher { $tenant }

  @OGInjected(\OGTenantContainer.publisherService) private var publisherService

  /// Initializes the tenant observer.
  public init() {
    receiveTenantUpdates()
  }

  private func receiveTenantUpdates() {
    publisherService.tenantDidChangePublisher
      .removeDuplicates(by: { $0.identifier == $1.identifier })
      .map { $0 }
      .assign(to: &$tenant)
  }
}
