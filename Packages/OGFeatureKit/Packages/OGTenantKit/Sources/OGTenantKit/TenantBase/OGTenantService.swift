import Combine
import Foundation

import OGCore
import OGDIService
import OGLogger
import OGTenantCore

// MARK: - OGTenantService

/// This class provides tenant related functionality such as fetching and selecting a tenant.
/// Fetching a List of Tenants
///
/// To fetch a list of tenants, you can use the tenants property of the tenantService object. This property returns an array of OGTenant objects, which represent the tenants in the system.
///
/// To obtain a reference to the tenantService object, you can use the @OGInjected property wrapper and inject the service using the OGTenantContainer.service key. For example:
/// ```swift
/// class MyClass {
/// 	 @OGInjected(OGTenantContainer.service) private var tenantService: OGTenantServicing
///
/// 	 func fetchTenants() {
/// 		 let tenants = tenantService.tenants  // Fetch the list of tenants
/// 		 print(tenants)  // Outputs the list of tenants
/// 	 }
/// }
/// ```
///
/// Selecting a Tenant
///
/// To select a tenant, you can use the setTenant(for:) method of the tenantService object. This method sets the tenant with the specified identifier as the selected tenant.
///
/// To call the setTenant(for:) method, you can use the tenantService object obtained as described above. For example:
/// ```swift
/// class MyClass {
/// 	 @OGInjected(OGTenantContainer.service) private var tenantService: OGTenantServicing
///
/// 	 func selectTenant(withIdentifier tenantIdentifier: OGIdentifier) {
/// 		 try tenantService.setTenant(for: tenantIdentifier)
/// 	 }
/// }
/// ```
///
/// Listening for Tenant Changes
///
/// To listen for tenant changes, you can use the selectedTenantPublished publisher of the tenantService object. This publisher emits the currently selected tenant whenever it changes.
///
/// To listen for tenant changes, you can subscribe to the selectedTenantPublished publisher and update your code accordingly. For example:
///
/// ```swift
/// class MyClass {
/// 	 @OGInjected(OGTenantContainer.service) private var tenantService: OGTenantServicing
///
/// 	 func listenForTenantChanges() {
/// 		 tenantService.selectedTenantPublished
/// 				 .sink { tenant in
/// 						 guard let tenant = tenant else { return }
/// 						 print("Selected tenant has changed to:", tenant)
/// 						 // Update your code as needed
/// 				 }
/// 				 .store(in: &tenantChangeSubscriptions)
/// 	}
/// ```
///
public final class OGTenantService: OGTenantServiceable {
  @OGInjected(\OGCoreContainer.storage) private var cache
  @OGInjected(\OGTenantContainer.publisherService) private var publisherService
  @OGInjected(\OGTenantContainer.detectionService) private var tenantDetector
  @OGInjected(\OGTenantContainer.fetcherService) private var fetcher
  @OGInjected(\OGCoreContainer.logger) private var logger
  @OGInjected(\OGTenantContainer.observerService) private var tenantSelectionObserver
  @OGInjected(\OGTenantContainer.selectionService) private var tenantSelection
  /// The currently selected tenant.
  public var selectedTenant: OGTenant? { _selectedTenant }
  /// A publisher for the currently selected tenant.
  public var selectedTenantPublished: Published<OGTenant?>.Publisher { $_selectedTenant }

  public enum OGTenantError: Error {
    /// An error that can be thrown when the default tenant is not found.
    case defaultTenantNotFound
  }

  private let localeProvider: OGLocaleProvidable

  private var model: OGTenantList = .empty
  @Published private var _selectedTenant: OGTenant?

  /// Initializes the tenant service.
  /// - Throws: OGTenantError.defaultTenantNotFound if a default tenant cannot be found or the tenant list could not be loaded.
  public init(localeProvider: OGLocaleProvidable = OGLocaleProvider()) throws {
    self.localeProvider = localeProvider
    self.model = try fetcher.fetch()
    if let cachedTenant {
      publisherService.publish(new: cachedTenant)
    } else if let identifier = try? tenantIdentifierForUserLocale() {
      try setTenant(for: identifier)
    } else if let singleTenant = model.tenants.first, model.tenants.count == 1 {
      try setTenant(for: singleTenant.identifier)
    } else if let defaultTenant = model.tenants.first(where: { $0.isDefault }) {
      try setTenant(for: defaultTenant.identifier)
    } else {
      throw OGTenantError.defaultTenantNotFound
    }
    tenantSelectionObserver.selectedTenant.assign(to: &$_selectedTenant)
  }

  private var cachedTenantIdentifier: OGIdentifier? {
    if let identifier = cache.value(forKey: OGPersistenceKey.Tenant.identifier) as? String {
      return try? OGIdentifier(identifier)
    }
    return nil
  }

  private var cachedTenant: OGTenant? {
    if
      let identifier = cachedTenantIdentifier,
      let tenant = try? model.tenant(for: identifier) {
      return tenant
    }
    return nil
  }
}

// MARK: OGTenantLocaleIdentifiable

extension OGTenantService: OGTenantLocaleIdentifiable {
  /// Gets the tenant identifier for the current user locale.
  /// - Returns: The tenant identifier.
  /// - Throws: An error if the tenant cannot be found.
  public func tenantIdentifierForUserLocale() throws -> OGIdentifier {
    let tenant = try tenantDetector.tenant(for: localeProvider.locale, from: model.tenants)
    return tenant.identifier
  }
}

// MARK: TenantsProvidable

extension OGTenantService: TenantsProvidable {
  /// A list of tenants.
  public var tenants: [OGTenant] {
    model.tenants
  }
}

// MARK: OGTenantSelectable

extension OGTenantService: OGTenantSelectable {
  /// Selects a tenant for the given identifier.
  /// - Parameter identifier: The identifier of the tenant to select.
  /// - Throws: An error if the tenant cannot be found.
  public func setTenant(for identifier: OGIdentifier) throws {
    try publisherService.publish(new: model.tenant(for: identifier))
    cache.persist(value: identifier.value, forKey: OGPersistenceKey.Tenant.identifier)
    logger.log(.debug, domain: .service, message: "Did set tenant for identifier: \(identifier)")
  }
}
