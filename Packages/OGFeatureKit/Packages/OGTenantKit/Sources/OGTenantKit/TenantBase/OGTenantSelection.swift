import Combine
import Foundation
import OGDIService
import OGTenantCore

/// This class provides tenant selection functionality.
public class OGTenantSelection: TenantSelectionProvidable {
  public enum TenantError: Error {
    /// An error that can be thrown when no tenant is selected.
    case noTenantSelected
  }

  @OGInjected(\OGTenantContainer.observerService) private var tenantObserver
  private var cancellables: Set<AnyCancellable> = []

  /// The currently selected tenant.
  public var tenant: OGTenant?

  /// Initializes the tenant selection.
  public init() {
    receiveTenantUpdates()
  }

  private func receiveTenantUpdates() {
    tenantObserver.selectedTenant.sink { [weak self] in
      self?.tenant = $0
    }.store(in: &cancellables)
  }
}
