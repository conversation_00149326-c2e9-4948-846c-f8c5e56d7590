import Foundation
import OGDIService

// MARK: - OGTenantContainer

/// This class provides a container for tenant related services.
public final class OGTenantContainer: OGDISharedContainer {
  public static var shared: OGTenantContainer = .init()

  public var manager: OGDIContainerManager = .init()

  /// A service for accessing tenant related functionality.
  public var service: OGDIService<OGTenantServiceable> {
    self {
      do {
        return try OGTenantService()
      } catch {
        fatalError(error.localizedDescription)
      }
    }.cached
  }

  /// A service for observing tenant selection.
  public var observerService: OGDIService<OGTenantObservable> {
    self {
      OGTenantObserver()
    }.cached
  }

  /// A service for providing tenant selection.
  public var selectionService: OGDIService<TenantSelectionProvidable> {
    self {
      OGTenantSelection()
    }.cached
  }

  internal var detectionService: OGDIService<OGTenantDetectable> {
    self {
      OGTenantDetectionService()
    }
  }

  internal var fetcherService: OGDIService<any OGTenantListFetchable> {
    self {
      OGTenantBundledFetcher()
    }
  }

  public var publisherService: OGDIService<OGTenantPublishable> {
    self {
      OGTenantPublisher()
    }.cached
  }
}
