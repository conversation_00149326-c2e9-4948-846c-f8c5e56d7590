import Foundation
import OGIdentifier

/// This protocol defines an object that can identify the tenant for the current user's locale.
public protocol OGTenantLocaleIdentifiable {
  /// Gets the tenant identifier for the current user's locale.
  /// - Throws: An error if no tenant is found for the current user's locale.
  /// - Returns: The tenant identifier for the current user's locale.
  func tenantIdentifierForUserLocale() throws -> OGIdentifier
}
