import Combine
import Foundation
import OGIdentifier
import OGTenantCore

/// This protocol defines a service for managing tenant selection.
public protocol OGTenantServiceable {
  /// The currently selected tenant.
  var selectedTenant: OGTenant? { get }
  /// A publisher for tenant selection updates.
  var selectedTenantPublished: Published<OGTenant?>.Publisher { get }
  /// The available tenants.
  var tenants: [OGTenant] { get }
  /// Sets the selected tenant by identifier.
  /// - Parameter identifier: The identifier of the tenant to select.
  /// - Throws: An error if no tenant with the given identifier exists.
  func setTenant(for identifier: OGIdentifier) throws
}
