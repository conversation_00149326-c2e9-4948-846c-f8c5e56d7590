import Foundation
import OGCore
import OGDIService
import OGLogger
import OGTenantCore

/// This class provides tenant detection functionality.
open class OGTenantDetectionService: OGTenantDetectable {
  @OGInjected(\OGCoreContainer.logger) var logger

  /// Initializes the tenant detection service.
  public init() {}

  /// Detects the tenant that matches the given locale from the given tenants.
  /// - Parameters:
  ///   - locale: The locale to match.
  ///   - tenants: The tenants to search through.
  /// - Throws: An error if no tenant can be found that matches the given locale.
  /// - Returns: The tenant that matches the given locale.
  open func tenant(for locale: Locale, from tenants: [OGTenant]) throws -> OGTenant {
    if let match = tenants.first(where: { $0.matches(locale) }) {
      logger.log(.debug, domain: .service, message: "Direct match of locale: \(match.identifier.value)")
      return match
    } else if let supported = tenants.first(where: { $0.explicitlySupports(locale) }) {
      logger.log(.debug, domain: .service, message: "User locale explicitly supported by tenant: \(supported.identifier.value)")
      return supported
    } else if let supported = tenants.first(where: { $0.supportsRegion(for: locale) }) {
      logger.log(.debug, domain: .service, message: "User region supported by tenant: \(supported.identifier.value)")
      return supported
    } else if let supported = tenants.first(where: { $0.supportsLanguage(for: locale) }) {
      logger.log(.debug, domain: .service, message: "User locale supported by tenant: \(supported.identifier.value)")
      return supported
    } else {
      throw OGTenantIdentifiableError.noMatch(forLocale: locale.identifier)
    }
  }
}
