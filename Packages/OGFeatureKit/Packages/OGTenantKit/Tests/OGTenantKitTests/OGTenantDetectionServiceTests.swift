import OGIdentifier
import OGT<PERSON>tCore
@testable import OGTenantKit
import XCTest

final class OGTenantDetectionServiceTests: XCTestCase {
  func test_localeMatchesTenant_returnMatch() throws {
    let tenantOne = try OGTenant(
      locale: Locale(identifier: "de-DE"),
      displayName: "TenantOne",
      supportedLocales: ["ro-RO"]
    )
    let tenantTwo = try OGTenant(
      locale: Locale(identifier: "fr-FR"),
      displayName: "TenantTwo",
      supportedLocales: []
    )

    let result = try OGTenantDetectionService().tenant(for: Locale(identifier: "de-DE"), from: [tenantOne, tenantTwo])

    XCTAssertEqual(result.identifier.value, "de-DE")
    XCTAssertEqual(result.locale, Locale(identifier: "de-DE"))
    XCTAssertEqual(result.supportedLocales, [Locale(identifier: "ro-RO")])
    XCTAssertEqual(result.displayName, "TenantOne")
    XCTAssertEqual(result.currencyCode?.value, "EUR")
    XCTAssertEqual(result.languageCode?.value, "de")
    XCTAssertEqual(result.countryCode?.value, "DE")
  }

  func test_localeMatchesSupported_returnSupported() throws {
    let tenantOne = try OGTenant(
      locale: Locale(identifier: "de-DE"),
      displayName: "TenantOne",
      supportedLocales: ["ro-RO"]
    )
    let tenantTwo = try OGTenant(
      locale: Locale(identifier: "fr-FR"),
      displayName: "TenantTwo",
      supportedLocales: []
    )

    let result = try OGTenantDetectionService().tenant(for: Locale(identifier: "ro-RO"), from: [tenantOne, tenantTwo])

    XCTAssertEqual(result.identifier.value, "de-DE")
    XCTAssertEqual(result.locale, Locale(identifier: "de-DE"))
    XCTAssertEqual(result.supportedLocales, [Locale(identifier: "ro-RO")])
    XCTAssertEqual(result.displayName, "TenantOne")
    XCTAssertEqual(result.currencyCode?.value, "EUR")
    XCTAssertEqual(result.languageCode?.value, "de")
    XCTAssertEqual(result.countryCode?.value, "DE")
  }

  func test_localeMatchesSupported_return_supported_region() throws {
    let tenantOne = try OGTenant(
      locale: Locale(identifier: "de-DE"),
      displayName: "TenantOne",
      supportedLocales: ["all-DE"]
    )
    let tenantTwo = try OGTenant(
      locale: Locale(identifier: "fr-FR"),
      displayName: "TenantTwo",
      supportedLocales: []
    )

    let result = try OGTenantDetectionService().tenant(for: Locale(identifier: "en-DE"), from: [tenantOne, tenantTwo])

    XCTAssertEqual(result.identifier.value, "de-DE")
    XCTAssertEqual(result.locale, Locale(identifier: "de-DE"))
    XCTAssertEqual(result.supportedLocales, [Locale(identifier: "all-DE")])
    XCTAssertEqual(result.displayName, "TenantOne")
    XCTAssertEqual(result.currencyCode?.value, "EUR")
    XCTAssertEqual(result.languageCode?.value, "de")
    XCTAssertEqual(result.countryCode?.value, "DE")
  }

  func test_localeMatchesSupported_return_supported_language() throws {
    let tenantOne = try OGTenant(
      locale: Locale(identifier: "de-DE"),
      displayName: "TenantOne",
      supportedLocales: ["de-ALL"]
    )
    let tenantTwo = try OGTenant(
      locale: Locale(identifier: "fr-FR"),
      displayName: "TenantTwo",
      supportedLocales: []
    )

    let result = try OGTenantDetectionService().tenant(for: Locale(identifier: "de-AT"), from: [tenantOne, tenantTwo])

    XCTAssertEqual(result.identifier.value, "de-DE")
    XCTAssertEqual(result.locale, Locale(identifier: "de-DE"))
    XCTAssertEqual(result.supportedLocales, [Locale(identifier: "de-ALL")])
    XCTAssertEqual(result.displayName, "TenantOne")
    XCTAssertEqual(result.currencyCode?.value, "EUR")
    XCTAssertEqual(result.languageCode?.value, "de")
    XCTAssertEqual(result.countryCode?.value, "DE")
  }

  func test_localeDoesNotMatch_throwError() throws {
    let tenantOne = try OGTenant(
      locale: Locale(identifier: "de-DE"),
      displayName: "TenantOne",
      supportedLocales: ["de-ALL"]
    )
    let tenantTwo = try OGTenant(
      locale: Locale(identifier: "fr-FR"),
      displayName: "TenantTwo",
      supportedLocales: []
    )
    let expectation = XCTestExpectation(description: "Check if error is thrown.")
    XCTAssertThrowsError(try OGTenantDetectionService().tenant(for: Locale(identifier: "ro-RO"), from: [tenantOne, tenantTwo])) { error in
      switch error {
      case let OGTenantIdentifiableError.noMatch(forLocale: locale):
        XCTAssertEqual(locale, Locale(identifier: "ro-RO").identifier)
      default:
        XCTAssertThrowsError("Wrong error thrown: \(error)")
      }
      expectation.fulfill()
    }

    wait(for: [expectation], timeout: 1.0)
  }
}
