import Combine
import OGIdentifier
import OGTenantCore
import OGTenantCoreTestsUtils
import XCTest

@testable import OGTenantKit

// MARK: - OGSelectedTenantObserverTests

final class OGSelectedTenantObserverTests: XCTestCase {
  var cancellables = Set<AnyCancellable>()

  override func tearDownWithError() throws {
    cancellables.removeAll()
    try super.tearDownWithError()
  }

  func test_selectedTenant_publishedOnChange() throws {
    // Arrange

    let observerMock = TenantObserverMock()
    OGTenantContainer.shared.observerService.register {
      observerMock
    }
    let sut = OGSelectedTenantObserver()
    let tenant = try OGTenant()

    let expectation = XCTestExpectation(description: "Should be called after tenant change on tenantObserver")
    sut.$selectedTenant.sink { optionalTenant in

      guard let updatedTenant = optionalTenant else { return }

      XCTAssertEqual(updatedTenant.identifier, tenant.identifier)
      XCTAssertEqual(updatedTenant.locale, tenant.locale)
      XCTAssertEqual(updatedTenant.supportedLocales, tenant.supportedLocales)
      XCTAssertEqual(updatedTenant.displayName, tenant.displayName)
      XCTAssertEqual(updatedTenant.currencyCode?.value, tenant.currencyCode?.value)
      XCTAssertEqual(updatedTenant.languageCode?.value, tenant.languageCode?.value)
      XCTAssertEqual(updatedTenant.countryCode?.value, tenant.countryCode?.value)
      expectation.fulfill()
    }.store(in: &cancellables)
    // Act

    XCTAssertNil(sut.selectedTenant)
    observerMock.setNewTenant(tenant)
    // Assert
    wait(for: [expectation], timeout: 1.0)
  }

  func test_selectedTenant_publishedOnChange_emptyTenant() throws {
    // Arrange

    let observerMock = TenantObserverMock()
    OGTenantContainer.shared.observerService.register {
      observerMock
    }
    let sut = OGSelectedTenantObserver()
    let expectation = XCTestExpectation(description: "Should be called after tenant change on tenantObserver")

    var isFirstCall = true
    sut.$selectedTenant.sink { optionalTenant in

      guard !isFirstCall else {
        isFirstCall.toggle()
        return
      }

      XCTAssertNil(optionalTenant)
      expectation.fulfill()
    }.store(in: &cancellables)
    // Act

    XCTAssertNil(sut.selectedTenant)
    observerMock.setNewTenant(nil)
    // Assert
    wait(for: [expectation], timeout: 1.0)
  }
}

// MARK: - TenantObserverMock

private class TenantObserverMock: OGTenantObservable {
  @Published private var tenant: OGTenant?

  public var selectedTenant: Published<OGTenant?>.Publisher { $tenant }

  init() {}

  func setNewTenant(_ newTenant: OGTenant?) {
    tenant = newTenant
  }
}
