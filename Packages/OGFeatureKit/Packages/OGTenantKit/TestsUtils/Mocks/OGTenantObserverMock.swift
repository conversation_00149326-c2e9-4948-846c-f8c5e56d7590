import Combine
import OGCore
import OGDIService
import OGIdentifier
import <PERSON>GMock
import OGTenantCore
import OGTenantKit

@OGMock
public final class OGTenantObserverMock: OGTenantObservable {
  public init() {
    let stub: OGTenant? = OGTenant.stub
    mock.selectedTenant.getter.mockCall {
      var published = Published(initialValue: stub)
      return published.projectedValue
    }
  }

  public var selectedTenant: Published<OGTenant?>.Publisher {
    mock.selectedTenant.getter.record()
  }
}
