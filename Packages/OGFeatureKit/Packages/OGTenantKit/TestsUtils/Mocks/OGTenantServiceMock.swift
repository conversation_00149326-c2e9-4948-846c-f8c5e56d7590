import Combine
import OGCore
import OGDIService
import OGIdentifier
import OGTenantCore
import OGTenantCoreTestsUtils
import OGTenantKit

// MARK: - OGTenantServiceMock

public class OGTenantServiceMock: OGTenantServiceable, Equatable {
  public static func == (lhs: OGTenantServiceMock, rhs: OGTenantServiceMock) -> Bool {
    lhs.selectedTenant?.identifier == rhs.selectedTenant?.identifier
  }

  public var selectedTenant: OGTenant? { _selectedTenant }
  public var selectedTenantPublished: Published<OGTenant?>.Publisher { $_selectedTenant }
  public var tenants: [OGTenant] {
    _tenants
  }

  @Published private var _selectedTenant: OGTenant?
  private var _tenants: [OGTenant]
  public init(tenants: [OGTenant]) {
    self._tenants = tenants
    self._selectedTenant = tenants.first(where: { $0.isDefault })
  }

  public func setTenant(for identifier: OGIdentifier) throws {
    _selectedTenant = tenants.first(where: { $0.identifier == identifier })
  }
}

// MARK: - OGTenantContainer + AutoRegistering

extension OGTenantContainer: AutoRegistering {
  public func autoRegister() {
    guard OGCoreContainer.shared.appEnvironment().isTestsBuild else { return }
    service.register {
      OGTenantServiceMock(tenants: .stub)
    }
    observerService.register {
      OGTenantObserverMock()
    }
  }
}
