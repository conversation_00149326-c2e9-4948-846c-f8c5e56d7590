import Foundation
import OGDIService
import OGFeatureKit

extension TestEnvironmentsListView {
  class ViewModel: ObservableObject {
    struct Model: Identifiable {
      let id: String
      let name: String
      let isSelected: Bool
    }

    enum Action {
      case selectTestEnvironment(Model)
      case fetchTestEnvironments
      case deselectTestEnvironment
    }

    @Published var testEnvironments: [Model] = []
    @Published var isRelease: Bool = false

    @OGInjected(\OGTestEnvironmentsContainer.testEnvironmentsService) private var testEnvironmentsService

    private func fetchTestEnvironments() {
      testEnvironments = testEnvironmentsService.environments
        .sorted(by: { $0.identifier.value < $1.identifier.value })
        .map {
          let isSelected = testEnvironmentsService.testEnvironment?.identifier.value ?? "" == $0.identifier.value
          return Model(id: $0.identifier.value, name: $0.identifier.value, isSelected: isSelected)
        }
      isRelease = testEnvironmentsService.testEnvironment == nil
    }

    private func onTestEnvironmentSelect(_ testEnvironment: Model) {
      guard
        let identifier = testEnvironmentsService.environments
          .first(where: { testEnvironment.id == $0.identifier.value })?.identifier else { return }
      try? testEnvironmentsService.selectTestEnvironment(for: identifier)
      fetchTestEnvironments()
    }
  }
}
