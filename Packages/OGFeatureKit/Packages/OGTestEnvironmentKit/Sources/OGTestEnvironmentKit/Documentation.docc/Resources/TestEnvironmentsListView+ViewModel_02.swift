import Foundation
import OGDIService
import OGFeatureKit

extension TestEnvironmentsListView {
  class ViewModel: ObservableObject {
    struct Model: Identifiable {
      let id: String
      let name: String
      let isSelected: Bool
    }

    enum Action {
      case selectTestEnvironment(Model)
      case fetchTestEnvironments
      case deselectTestEnvironment
    }

    @Published var testEnvironments: [Model] = []
    @Published var isRelease: Bool = false
  }
}
