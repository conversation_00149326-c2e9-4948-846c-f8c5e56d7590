# ``OGTestEnvironmentKit``

The OGTestEnvironmentKit is a framework for managing OGTestEnvironment objects in an iOS app. The OGTestEnvironment objects represent sets of features that can be enabled or disabled for testing purposes.

## Overview

### OGTestEnvironmentsService
The ``OGTestEnvironmentsService`` is the main entry point, that allows to select and deselect OGTestEnvironment objects, and provides access to the selected OGTestEnvironment object. It also receives updates about the available OGTestEnvironment objects, and persists the selected OGTestEnvironment object in the local cache.

The service is intended to be used in a debug or beta build of the app, as the testEnvironment property and the selectTestEnvironment(for:) and deselectTestEnvironment() methods are only accessible in these build configurations. In other build configurations, the testEnvironment property returns nil, and the selectTestEnvironment(for:) and deselectTestEnvironment() methods do not have any effect. It has the following properties and methods:


#### Properties

- ``OGTestEnvironmentsService/testEnvironment``: The currently selected `OGTestEnvironment` object, or `nil` if no test environment has been selected for the current tenant.
- ``OGTestEnvironmentsService/environments``: An array of all available `OGTestEnvironment` objects for the current tenant.

##### Methods

- ``OGTestEnvironmentsService/selectTestEnvironment(for:)``: Sets the selected `OGTestEnvironment` object to the current tenant with the specified identifier. This method may throw an error if the test environment cannot be found.
- ``OGTestEnvironmentsService/deselectTestEnvironment()``: Deselects the current `OGTestEnvironment` object for the current tenant.

## Topics

### Essentials
- <doc:/tutorials/Tutorial-Table-of-Contents>
