import Combine
import Foundation
import OGDIService
import OGFeatureCore
import OGTenantKit

/// A class that is a concrete implementation of the `OGTestEnvironmentFeatureSetPublishable` protocol.
/// It publishes new `OGTestEnvironment` objects as feature sets by using the `OGTestEnvironmentsContainer.testEnvironmentPersistanceService`
/// and the `OGTenantContainer.observerService` for fetching and observing tenant updates, respectively.
public class OGTestEnvironmentFeatureSetPublisher: OGTestEnvironmentFeatureSetPublishable {
  @Published private var testEnvironmentFeatureSetDidChange: Set<OGFeature> = []
  @OGInjected(\OGTestEnvironmentsContainer.testEnvironmentPersistanceService) private var testEnvironmentPersistanceService
  @OGInjected(\OGTenantContainer.observerService) private var tenantObserver
  private var cancellables = Set<AnyCancellable>()

  /// Initializes a new `OGTestEnvironmentFeatureSetPublisher` object and starts observing tenant updates.
  public init() {
    receiveTenantUpdates()
  }

  private func receiveTenantUpdates() {
    tenantObserver.selectedTenant
      .compactMap { $0 }
      .sink { [weak self] tenant in
        guard let testEnvironment = try? self?.testEnvironmentPersistanceService.cached(identifier: tenant.identifier) else { return }
        self?.publish(new: testEnvironment)
      }.store(in: &cancellables)
  }

  /// Publishes the specified `testEnvironment` as a new feature set.
  /// - Parameter testEnvironment: The `OGTestEnvironment` object to be published as a new feature set.
  public func publish(new testEnvironment: OGTestEnvironment?) {
    testEnvironmentFeatureSetDidChange = testEnvironment?.features ?? []
  }

  /// A publisher that broadcasts changes to the current set of `OGFeature` objects in the `OGTestEnvironment`.
  public var featureSetDidChangePublisher: AnyPublisher<Set<OGFeature>, Never> {
    $testEnvironmentFeatureSetDidChange
      .eraseToAnyPublisher()
  }
}
