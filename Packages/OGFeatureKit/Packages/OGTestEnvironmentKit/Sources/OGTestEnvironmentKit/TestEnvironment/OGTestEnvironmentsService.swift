import Combine
import Foundation
import OGCore
import OGDIService
import OGFeatureCore
import OGFeatureManager
import OGIdentifier
import <PERSON>GLogger
import OGMacros
import OGStorage
import OGTenantCore
import OGTenantKit

// MARK: - OGTestEnvironmentsServiceable

/// A protocol that defines an object that can provide and select `OGTestEnvironment` objects.
public protocol OGTestEnvironmentsServiceable: TestEnvironmentProvidable, TestEnvironmentSelectable, TestEnvironmentCreatable {
  /// An array of `OGTestEnvironment` objects.
  var preConfiguredEnvironments: CurrentValueSubject<[OGTestEnvironment], Never> { get }
  /// An temperarly array of `OGTestEnvironment` objects.
  var customEnvironments: CurrentValueSubject<[OGTestEnvironment], Never> { get }
  /// Holds the value of the current test environment
  var currentTestEnvironment: CurrentValueSubject<OGTestEnvironment?, Never> { get }
}

// MARK: - OGTestEnvironmentsService

/// A class provides and selects `OGTestEnvironment` objects.
public final class OGTestEnvironmentsService: OGTestEnvironmentsServiceable {
  @OGInjected(\OGCoreContainer.logger) private var logger
  @OGInjected(\OGCoreContainer.appEnvironment) private var appEnvironment
  @OGInjected(\OGTenantContainer.observerService) private var tenantObserver
  /// Requires a shared scope for TestEnvironmentsFeatureAdapter
  @OGInjected(\OGTestEnvironmentsContainer.featureAdapter) private var provider
  @OGInjected(\OGTestEnvironmentsContainer.testEnvironmentFeatureSetPublisher) private var testEnvironmentFeatureSetPublisher
  @OGInjected(\OGTestEnvironmentsContainer.testEnvironmentPersistanceService) private var testEnvironmentPersistanceService

  public var preConfiguredEnvironments = CurrentValueSubject<[OGTestEnvironment], Never>([])
  public var customEnvironments = CurrentValueSubject<[OGTestEnvironment], Never>([])
  public var currentTestEnvironment = CurrentValueSubject<OGTestEnvironment?, Never>(nil)

  // MARK: - TestEnvironmentsUpdateReceivable

  private var cancellables: Set<AnyCancellable> = []
  private var currentTenant: OGTenant?

  @Published private var testEnvironmentFeatureSet: Set<OGFeature> = []

  /// Initializes the `OGTestEnvironmentsService` instance and subscribes to updates on the set of `OGTestEnvironment` objects if the App runs in debug or beta
  public init() {
    setup()
    loadPersistedCustomEnvironments()
  }

  private func setup() {
    guard appEnvironment.isDebugOrBetaBuild else {
      deselectTestEnvironment()
      cleanCache()
      logger.log(.debug, domain: .service, message: "Skipped setup for TestEnvironment due to current build configuration.")
      return
    }

    receiveUpdates()
  }

  private func loadPersistedCustomEnvironments() {
    do {
      let persistedCustomEnvironments = try testEnvironmentPersistanceService.loadCustomEnvironments() ?? []
      customEnvironments.send(persistedCustomEnvironments)
    } catch {
      logger.log(.debug, domain: .service, message: "Failed to load persisted custom environments: \(error)")
    }
  }

  private func receiveUpdates() {
    provider.preConfiguredEnvironmentSetDidUpdatePublisher
      .sink { [weak self] environments in
        self?.preConfiguredEnvironments.send(Array(environments))
        self?.logger.log(.debug, domain: .service, message: "did publish new pre configured TestEnvironments for \(self?.currentTenant?.identifier.value ?? "NAN")")
      }
      .store(in: &cancellables)

    provider.customEnvironmentSetDidUpdatePublisher
      .sink { [weak self] environments in
        self?.customEnvironments.send(Array(environments))
        self?.logger.log(.debug, domain: .service, message: "did publish new custom TestEnvironments for \(self?.currentTenant?.identifier.value ?? "NAN")")
      }
      .store(in: &cancellables)

    tenantObserver.selectedTenant.sink { [weak self] tenant in
      self?.currentTenant = tenant
      if let identifier = self?.currentTenant?.identifier {
        self?.resetTestEnvironment(for: identifier)
      } else {
        self?.logger.log(.debug, domain: .service, message: "TenantEnvironments did change without valid tenant.")
      }
    }.store(in: &cancellables)
  }

  private func cleanCache() {
    testEnvironmentPersistanceService.delete()
  }

  private func resetTestEnvironment(for tenantIdentifier: OGIdentifier) {
    do {
      selectedTestEnvironment = try testEnvironmentPersistanceService.cached(identifier: tenantIdentifier)
    } catch {
      logger.log(.critical, domain: .service, message: error.localizedDescription)
      selectedTestEnvironment = nil
    }
  }

  private var selectedTestEnvironment: OGTestEnvironment? {
    didSet {
      if let selected = selectedTestEnvironment {
        // Only persist the selected environment if it is not a runtime environment
        if !customEnvironments.value.contains(where: { $0.identifier == selected.identifier }) {
          currentTestEnvironment.send(selectedTestEnvironment)
          persist(selected)
        }
      } else if let identifier = currentTenant?.identifier {
        try? testEnvironmentPersistanceService.removeCachedTestEnvironment(for: identifier)
      }
      testEnvironmentFeatureSetPublisher.publish(new: selectedTestEnvironment)
    }
  }

  private func persist(_ testEnvironment: OGTestEnvironment) {
    guard let identifier = currentTenant?.identifier else {
      logger.log(.debug, domain: .service, message: "Can't persist testEnvironment \(testEnvironment.identifier.value) due to missing tenantIdentifier.")
      return
    }
    try? testEnvironmentPersistanceService.persist(testEnvironment, identifier: identifier)
  }
}

// MARK: TestEnvironmentProvidable

extension OGTestEnvironmentsService: TestEnvironmentProvidable {
  /// The selected `OGTestEnvironment` object.
  ///
  /// - Returns: The selected `OGTestEnvironment` object if it is available, `nil` otherwise.
  public var testEnvironment: OGTestEnvironment? {
    if appEnvironment.isDebugOrBetaBuild {
      if let selectedTestEnvironment {
        return selectedTestEnvironment
      } else {
        logger.log(.debug, domain: .service, message: "No cached testEnvironment")
        return nil
      }
    }
    logger.log(.debug, domain: .service, message: "testEnvironment is inaccessible due to current build configuration.")
    return nil
  }
}

// MARK: TestEnvironmentSelectable

extension OGTestEnvironmentsService: TestEnvironmentSelectable {
  /// Sets  the `OGTestEnvironment` object with the given identifier.
  /// - Parameter identifier: The identifier of the `OGTestEnvironment` object to set.
  /// - Throws: An error if the `OGTestEnvironment` object with the given identifier cannot be found.
  public func selectTestEnvironment(for identifier: OGIdentifier) throws {
    if appEnvironment.isDebugOrBetaBuild {
      let testEnvironment = try provider.testEnvironment(for: identifier)
      selectedTestEnvironment = testEnvironment
    } else {
      selectedTestEnvironment = nil
      logger.log(.debug, domain: .service, message: "TestEnvironment (\(identifier.value)) is inaccessible due to current build configuration.")
    }
  }

  /// Deselects the current `OGTestEnvironment` object.
  public func deselectTestEnvironment() {
    selectedTestEnvironment = nil
    currentTestEnvironment.send(nil)
  }
}

// MARK: TestEnvironmentCreatable

extension OGTestEnvironmentsService: TestEnvironmentCreatable {
  /// Creates a new test environment with the given parameters.
  ///
  /// - Parameters:
  ///   - name: The name of the test environment.
  ///   - url: The URL of the test environment.
  ///   - password: The password for the test environment.
  /// - Throws: An error if the environment cannot be created.
  public func createTestEnvironment(name: String, url: AnyJSONType, password: String) throws {
    let newEnvironment = try makeTestEnvironment(fromName: name, url: url, password: password)

    customEnvironments.value.append(newEnvironment)
    provider.updateCustomEnvironmentsSet(Set(customEnvironments.value))
    persistCustomEnvironments()
  }

  /// Updates a custom `OGTestEnvironment` object with a new value.
  /// - Parameters:
  ///   - name: The name for the test environment.
  ///   - url: The URL for the test environment.
  ///   - password: The password for the test environment.
  ///   - identifier: The identifier of the `OGTestEnvironment` object to be updated.
  public func updateCustomEnvironment(name: String, url: AnyJSONType, password: String, identifier: OGIdentifier) {
    do {
      let updatedEnvironment = try makeTestEnvironment(fromName: name, url: url, password: password)
      try testEnvironmentPersistanceService.updateCustomEnvironment(updatedEnvironment, identifier: identifier)
      loadPersistedCustomEnvironments()
    } catch {
      logger.log(.debug, domain: .service, message: "Failed to update custom environment: \(error)")
    }
  }

  /// Creates an `OGTestEnvironment` from the provided parameters.
  ///
  /// - Parameters:
  ///   - name: The name for the test environment.
  ///   - url: The URL for the test environment.
  ///   - password: The password for the test environment.
  /// - Returns: The created `OGTestEnvironment` object.
  private func makeTestEnvironment(fromName name: String, url: AnyJSONType, password: String) throws -> OGTestEnvironment {
    guard let urlString = url.jsonValue as? String, let host = extractHost(from: urlString) else {
      throw CustomError.invalidURL
    }

    let baseUrlIdentifier = #identifier("baseUrl")
    let authIdentifier = #identifier("auth")
    let environmentIdentifier = try OGIdentifier(name)

    let webFeature = OGFeature(identifier: baseUrlIdentifier, isEnabled: true, custom: ["web": url, "api": AnyJSONType(jsonValue: "")], features: [])
    let authFeature = createAuthFeature(
      identifier: authIdentifier,
      host: host,
      user: name,
      password: password
    )

    return OGTestEnvironment(identifier: environmentIdentifier, isEnabled: true, custom: [:], features: [webFeature, authFeature])
  }

  /// Removes a custom `OGTestEnvironment` object.
  /// - Parameter identifier: The identifier of the `OGTestEnvironment` object to be removed.
  public func removeCustomEnvironment(identifier: OGIdentifier) {
    do {
      try testEnvironmentPersistanceService.removeCustomEnvironment(identifier: identifier)
      loadPersistedCustomEnvironments()
    } catch {
      logger.log(.debug, domain: .service, message: "Failed to remove custom environment: \(error)")
    }
  }

  private func persistCustomEnvironments() {
    do {
      try testEnvironmentPersistanceService.persistCustomEnvironments(customEnvironments.value)
    } catch {
      logger.log(.debug, domain: .service, message: "Failed to persist custom environments: \(error)")
    }
  }

  /// Helper method to extract the host from a URL string.
  private func extractHost(from url: String) -> String? {
    guard let url = URL(string: url) else { return nil }
    return url.host
  }

  /// Helper method to create an authentication feature.
  private func createAuthFeature(identifier: OGIdentifier, host: String, user: String, authMethod: String = "NSURLAuthenticationMethodHTTPBasic", port: Int = 443, password: String) -> OGFeature {
    let customData: [String: AnyJSONType] = [
      "host": AnyJSONType(jsonValue: host),
      "realm": AnyJSONType(jsonValue: ""),
      "user": AnyJSONType(jsonValue: user),
      "authMethod": AnyJSONType(jsonValue: authMethod),
      "password": AnyJSONType(jsonValue: password),
      "port": AnyJSONType(jsonValue: port)
    ]

    return OGFeature(identifier: identifier, isEnabled: true, custom: customData, features: [])
  }

  enum CustomError: Error {
    case invalidURL
  }
}

// MARK: - TestEnvironmentSetProvidable

/// A protocol that provides a set of `OGTestEnvironment` objects.
public protocol TestEnvironmentSetProvidable {
  /// The set of `OGTestEnvironment` objects.
  var testEnvironments: Set<OGTestEnvironment> { get }
}

// MARK: - TestEnvironmentProvidable

/// A protocol that provides a single `OGTestEnvironment` object.
public protocol TestEnvironmentProvidable {
  /// The selected `OGTestEnvironment` object.
  var testEnvironment: OGTestEnvironment? { get }
}

// MARK: - TestEnvironmentSelectable

/// A protocol that allows selection and deselection of an `OGTestEnvironment` object.
public protocol TestEnvironmentSelectable {
  /// Selects the `OGTestEnvironment` object with the given identifier.
  ///
  /// - Parameter identifier: The identifier of the `OGTestEnvironment` object to select.
  /// - Throws: An error if the `OGTestEnvironment` object with the given identifier cannot be found.
  func selectTestEnvironment(for identifier: OGIdentifier) throws

  /// Deselects the current `OGTestEnvironment` object.
  func deselectTestEnvironment()
}

// MARK: - TestEnvironmentCreatable

/// A protocol that defines methods to create a test environment.
public protocol TestEnvironmentCreatable {
  /// Creates a new test environment with the given parameters.
  ///
  /// - Parameters:
  ///   - name: The name of the test environment.
  ///   - url: The URL of the test environment.
  ///   - password: The password for the test environment.
  /// - Throws: An error if the environment cannot be created.
  func createTestEnvironment(name: String, url: AnyJSONType, password: String) throws

  /// Updates a custom `OGTestEnvironment` object with a new value.
  /// - Parameters:
  ///   - name: The name for the test environment.
  ///   - url: The URL for the test environment.
  ///   - password: The password for the test environment.
  ///   - identifier: The identifier of the `OGTestEnvironment` object to be updated.
  func updateCustomEnvironment(name: String, url: AnyJSONType, password: String, identifier: OGIdentifier)

  /// Removes a custom `OGTestEnvironment` object.
  /// - Parameter identifier: The identifier of the `OGTestEnvironment` object to be removed.
  func removeCustomEnvironment(identifier: OGIdentifier)
}
