import Foundation
import OGCore
import OGDIService
import OGFeatureCore
import OGTenantKit

// MARK: - OGTestEnvironmentsFetchable

/// A  protocol thats defines a fetchable object that can retrieve `OGTestEnvironment` objects.
public protocol OGTestEnvironmentsFetchable: BundledJsonResourceFetchable {
  /// Fetches the `OGTestEnvironment` object with the specified `identifier`.
  /// - Parameter identifier: The identifier of the `OGTestEnvironment` to be retrieved.
  /// - Throws: An error of type `OGTenantSelection.TenantError` if the fetch fails.
  func fetch(identifier: String?) throws -> OGTestEnvironment
}

// MARK: - OGBundledTestEnvironmentsFetcher

/// A class that  is a concrete implementation of the `OGTestEnvironmentsFetchable` protocol.
/// It fetches `OGTestEnvironment` objects from a bundled JSON resource, using the specified `resourceName` suffix.
public final class OGBundledTestEnvironmentsFetcher: OGTestEnvironmentsFetchable {
  /// The type of object being fetched by this object.
  public typealias T = OGTestEnvironment
  private let featureName: String
  private let base: String
  private let bundle: Bundle?

  /// Initializes a new `OGBundledTestEnvironmentsFetcher` object with the specified `featureName`.
  /// - Parameter featureName: The  name for the TestEnvironments to use. The default value is "environments".
  /// - Parameter inBundle: The bundle to use to fetch the environments from. The default value is "main".
  /// - Parameter base: The base of the filename to use between the prefix (eg. de-DE) and the suffix (eg. beta). The default value is "_".
  public init(featureName: String = "environments", base: String = "_", inBundle bundle: Bundle? = Bundle.main) {
    self.featureName = featureName
    self.bundle = bundle
    self.base = base
  }

  /// Fetches the `OGTestEnvironment` object with the specified `identifier`.
  /// - Parameter identifier: The identifier of the `OGTestEnvironment` to be retrieved.
  /// - Throws: An error of type `OGTenantSelection.TenantError` if the fetch fails.
  public func fetch(identifier: String?) throws -> OGTestEnvironment {
    guard let tenantIdentifier = identifier else {
      throw OGTenantSelection.TenantError.noTenantSelected
    }

    let testEnvironments: [OGTestEnvironment] = try fetch(tenantIdentifier: tenantIdentifier)

    return try OGTestEnvironment(identifier: OGIdentifier(featureName), isEnabled: true, custom: [:], features: Set(testEnvironments))
  }

  private func fetch(tenantIdentifier: String) throws -> [OGTestEnvironment] {
    let resourceExtension = "json"
    guard let resourcePath = bundle?.resourcePath else {
      throw BundledResourceError.resourceNotFound("resourcePath in \(bundle.debugDescription)")
    }
    return try FileManager.default.contentsOfDirectory(atPath: resourcePath)
      .compactMap {
        let identifier = identifier(form: $0, with: resourceExtension)
        return identifier.range(of: "^\(tenantIdentifier)\(base).*", options: .regularExpression) != nil ? $0 : nil
      }
      .map {
        let identifier = identifier(form: $0, with: resourceExtension)
        let testEnvironment: OGFeatureSet = try Self.fetch(forResource: identifier, inBundle: bundle)
        return try OGTestEnvironment(identifier: OGIdentifier(String(identifier.dropFirst(tenantIdentifier.count + base.count))), isEnabled: true, custom: [:], features: testEnvironment.features)
      }
  }

  private func identifier(form resourcePath: String, with resourceExtension: String) -> String {
    resourcePath.components(separatedBy: "/").last?.components(separatedBy: ".\(resourceExtension)").first ?? ""
  }
}
