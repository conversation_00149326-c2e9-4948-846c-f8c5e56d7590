import Combine
import Foundation
import OGFeatureCore
import OGFeatureManager

/// A protocol that defines an object that can publish a new `OGTestEnvironment` object as a feature set.
public protocol OGTestEnvironmentFeatureSetPublishable: OGFeatureSetObservable {
  /// Publishes the specified `testEnvironment` as a new feature set.
  /// - Parameter testEnvironment: The `OGTestEnvironment` object to be published as a new feature set.
  func publish(new testEnvironment: OGTestEnvironment?)
}
