import Foundation
import OGCore
import OGDIService

// MARK: - OGTestEnvironmentPersistancable

/// A protocol that defines an object that can persist `OGTestEnvironment` objects.
public protocol OGTestEnvironmentPersistancable {
  /// Persists the specified `testEnvironment` object with the given `identifier`.
  /// - Parameters:
  ///   - testEnvironment: The `OGTestEnvironment` object to be persisted.
  ///   - identifier: The identifier to use when persisting the `testEnvironment` object.
  func persist(_ testEnvironment: OGTestEnvironment, identifier: OGIdentifier) throws

  /// Returns the persisted `OGTestEnvironment` object with the specified `identifier`, if it exists.
  /// - Parameter identifier: The identifier of the `OGTestEnvironment` object to be retrieved.
  /// - Throws: An error if the fetch fails.
  /// - Returns: The persisted `OGTestEnvironment` object with the specified `identifier`, if it exists.
  func cached(identifier: OGIdentifier) throws -> OGTestEnvironment?

  /// Removes the persisted `OGTestEnvironment` object with the specified `identifier`, if it exists.
  /// - Parameter identifier: The identifier of the `OGTestEnvironment` object to be removed.
  /// - Throws: An error if the removal fails.
  func removeCachedTestEnvironment(for identifier: OGIdentifier) throws

  /// Deletes all persisted `OGTestEnvironment` objects.
  func delete()

  /// Persists an array of custom `OGTestEnvironment` objects.
  /// - Parameter testEnvironments: The array of `OGTestEnvironment` objects to be persisted.
  func persistCustomEnvironments(_ testEnvironments: [OGTestEnvironment]) throws

  /// Returns the persisted array of custom `OGTestEnvironment` objects, if it exists.
  /// - Throws: An error if the fetch fails.
  /// - Returns: The persisted array of custom `OGTestEnvironment` objects, if it exists.
  func loadCustomEnvironments() throws -> [OGTestEnvironment]?

  /// Deletes all persisted custom `OGTestEnvironment` objects.
  /// - Throws: An error if the deletion fails.
  func deleteCustomEnvironments() throws

  /// Updates a custom `OGTestEnvironment` object with a new value.
  /// - Parameters:
  ///   - testEnvironment: The new `OGTestEnvironment` object.
  ///   - identifier: The identifier of the `OGTestEnvironment` object to be updated.
  func updateCustomEnvironment(_ testEnvironment: OGTestEnvironment, identifier: OGIdentifier) throws

  /// Removes a custom `OGTestEnvironment` object.
  /// - Parameter identifier: The identifier of the `OGTestEnvironment` object to be removed.
  func removeCustomEnvironment(identifier: OGIdentifier) throws
}

// MARK: - OGPersistenceKey.TestEnvironment

/// This enum defines keys for storing `OGTestEnvironment` objects in the local cache.
extension OGPersistenceKey {
  enum TestEnvironment: String, RawValueable {
    case identifier = "testEnvironmentIdentifier"
    case customEnvironments = "customTestEnvironments"
  }
}

// MARK: - OGTestEnvironmentPersistanceService

/// A class that is a concrete implementation of the `OGTestEnvironmentPersistancable` protocol.
/// It persists `OGTestEnvironment` objects using the `OGCoreContainer.storage` for storage.
class OGTestEnvironmentPersistanceService: OGTestEnvironmentPersistancable {
  @OGInjected(\OGCoreContainer.storage) private var cache
  @OGInjected(\OGCoreContainer.logger) private var logger

  /// Persists the specified `testEnvironment` object with the given `identifier`.
  /// - Parameters:
  ///   - testEnvironment: The `OGTestEnvironment` object to be persisted.
  ///   - identifier: The identifier to use when persisting the `testEnvironment` object.
  func persist(_ testEnvironment: OGTestEnvironment, identifier: OGCore.OGIdentifier) throws {
    var dic = try getPersistence() ?? [OGIdentifier: OGTestEnvironment]()
    dic[identifier] = testEnvironment
    try persist(dic)
  }

  /// Returns the persisted `OGTestEnvironment` object with the specified `identifier`, if it exists.
  /// - Parameter identifier: The identifier of the `OGTestEnvironment` object to be retrieved.
  /// - Throws: An error if the fetch fails.
  /// - Returns: The persisted `OGTestEnvironment` object with the specified `identifier`, if it exists.
  func cached(identifier: OGCore.OGIdentifier) throws -> OGTestEnvironment? {
    try getPersistence()?[identifier]
  }

  /// Removes the persisted `OGTestEnvironment` object with the specified `identifier`, if it exists.
  /// - Parameter identifier: The identifier of the OGTestEnvironment object to be removed.
  /// - Throws: An error if the removal fails.
  func removeCachedTestEnvironment(for identifier: OGCore.OGIdentifier) throws {
    guard var dic = try getPersistence() else { return }
    dic.removeValue(forKey: identifier)
    if dic.isEmpty {
      delete()
    } else {
      try persist(dic)
    }
  }

  /// Deletes all persisted OGTestEnvironment objects.
  func delete() {
    cache.delete(valueForKey: OGPersistenceKey.TestEnvironment.identifier)
  }

  private func getPersistence() throws -> [OGIdentifier: OGTestEnvironment]? {
    guard let data = cache.value(forKey: OGPersistenceKey.TestEnvironment.identifier) as? Data else { return nil }
    return try JSONDecoder().decode([OGIdentifier: OGTestEnvironment].self, from: data)
  }

  private func persist(_ dic: [OGIdentifier: OGTestEnvironment]) throws {
    let data = try JSONEncoder().encode(dic)
    cache.persist(value: data, forKey: OGPersistenceKey.TestEnvironment.identifier)
  }

  /// Persists an array of custom `OGTestEnvironment` objects.
  /// - Parameter testEnvironments: The array of `OGTestEnvironment` objects to be persisted.
  func persistCustomEnvironments(_ testEnvironments: [OGTestEnvironment]) throws {
    let data = try JSONEncoder().encode(testEnvironments)
    cache.persist(value: data, forKey: OGPersistenceKey.TestEnvironment.customEnvironments)
  }

  /// Returns the persisted array of custom `OGTestEnvironment` objects, if it exists.
  /// - Throws: An error if the fetch fails.
  /// - Returns: The persisted array of custom `OGTestEnvironment` objects, if it exists.
  func loadCustomEnvironments() throws -> [OGTestEnvironment]? {
    guard let data = cache.value(forKey: OGPersistenceKey.TestEnvironment.customEnvironments) as? Data else { return nil }
    return try JSONDecoder().decode([OGTestEnvironment].self, from: data)
  }

  /// Deletes all persisted custom `OGTestEnvironment` objects.
  /// - Throws: An error if the deletion fails.
  func deleteCustomEnvironments() throws {
    cache.delete(valueForKey: OGPersistenceKey.TestEnvironment.customEnvironments)
  }

  /// Updates a custom `OGTestEnvironment` object with a new value.
  /// - Parameters:
  ///   - testEnvironment: The new `OGTestEnvironment` object.
  ///   - identifier: The identifier of the `OGTestEnvironment` object to be updated.
  func updateCustomEnvironment(_ testEnvironment: OGTestEnvironment, identifier: OGIdentifier) throws {
    var customEnvironments = try loadCustomEnvironments() ?? []
    guard let index = customEnvironments.firstIndex(where: { $0.identifier == identifier }) else {
      logger.log(.debug, domain: .service, message: "Cannot find given identifier with test environment")
      throw EnvironmentError.environmentNotFound
    }
    customEnvironments[index] = testEnvironment
    try persistCustomEnvironments(customEnvironments)
  }

  /// Removes a custom `OGTestEnvironment` object.
  /// - Parameter identifier: The identifier of the `OGTestEnvironment` object to be removed.
  func removeCustomEnvironment(identifier: OGIdentifier) throws {
    var customEnvironments = try loadCustomEnvironments() ?? []
    guard let index = customEnvironments.firstIndex(where: { $0.identifier == identifier }) else {
      logger.log(.debug, domain: .service, message: "Cannot find given identifier with test environment")
      throw EnvironmentError.environmentNotFound
    }
    customEnvironments.remove(at: index)
    try persistCustomEnvironments(customEnvironments)
  }
}

// MARK: - EnvironmentError

enum EnvironmentError: Error {
  case environmentNotFound
}
