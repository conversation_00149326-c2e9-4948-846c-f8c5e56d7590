import Combine
import OGDIService
import OGFeatureCore
import OGIdentifier
import OGTenantCore
import OGTenantKit
import OGTestEnvironmentKit

// MARK: - TestEnvironmentsFeatureAdapterMock

public final class TestEnvironmentsFeatureAdapterMock: OGTestEnvironmentsFeatureAdaptable {
  public var preConfiguredEnvironments: Set<OGTestEnvironmentKit.OGTestEnvironment> = []

  public var customEnvironments: Set<OGTestEnvironmentKit.OGTestEnvironment> = []

  public var preConfiguredEnvironmentSetDidUpdatePublisher: AnyPublisher<Set<OGTestEnvironmentKit.OGTestEnvironment>, Never> = Just([]).eraseToAnyPublisher()

  public var customEnvironmentSetDidUpdatePublisher: AnyPublisher<Set<OGTestEnvironmentKit.OGTestEnvironment>, Never> = Just([]).eraseToAnyPublisher()

  @OGInjected(\OGTenantContainer.observerService) public var tenantObserver

  public var environments: Set<OGTestEnvironment> = []
  public var tenantUpdateClosure: ((OGTenant) -> Void) = { _ in }
  public var isAdaptedFeatureEnabled: Bool { true }
  public var environmentsSetUpdated: [Set<OGTestEnvironment>] = []

  @Published var environmentsSet: Set<OGTestEnvironment> = []
  public var testEnvironmentSetDidUpdatePublisher: AnyPublisher<Set<OGTestEnvironment>, Never> { $environmentsSet.eraseToAnyPublisher()
  }

  public var cancellables: Set<AnyCancellable> = []

  public var sortedEnvironmentIdentifiers: [OGIdentifierProvidable] {
    environments
      .sorted(by: { $0.identifier.value <= $1.identifier.value })
  }

  public init() {}

  public func testEnvironment(for identifier: OGIdentifier) throws -> OGTestEnvironment {
    guard
      identifier.value == "stage"
    else { throw OGFeatureIdentificationError.featureNotFound(identifier.value) }
    return OGTestEnvironment(
      identifier: identifier,
      isEnabled: true,
      custom: [:],
      features: []
    )
  }

  func updateEnvironmentsSet(_ newEnvironments: Set<OGTestEnvironmentKit.OGTestEnvironment>) {
    environments = newEnvironments
    environmentsSet = newEnvironments
    environmentsSetUpdated.append(newEnvironments)
  }

  public func updatePreconfiguredEnvironmentsSet(
    _: Set<OGTestEnvironmentKit.OGTestEnvironment>
  ) {}

  public func updateCustomEnvironmentsSet(
    _: Set<OGTestEnvironmentKit.OGTestEnvironment>
  ) {}
}
