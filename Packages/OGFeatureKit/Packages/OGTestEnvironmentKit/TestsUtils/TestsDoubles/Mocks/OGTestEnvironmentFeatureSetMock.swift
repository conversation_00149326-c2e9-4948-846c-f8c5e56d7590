import Combine
import Foundation
import OGFeatureCore
import OGIdentifierTestsUtils
import OGTestEnvironmentKit

public struct OGTestEnvironmentFeatureSetMock: OGTestEnvironmentFeatureSetPublishable, Equatable {
  private let uuid = UUID()
  public static func == (lhs: OGTestEnvironmentFeatureSetMock, rhs: OGTestEnvironmentFeatureSetMock) -> Bool {
    lhs.uuid == rhs.uuid
  }

  public init() {}

  public func publish(new _: OGTestEnvironment?) {}
  public var featureSetDidChangePublisher: AnyPublisher<Set<OGFeatureCore.OGFeature>, Never> =
    Just(
      Set(arrayLiteral: OGFeature(identifier: .stub, isEnabled: true, custom: [:], features: []))
    ).eraseToAnyPublisher()
}
