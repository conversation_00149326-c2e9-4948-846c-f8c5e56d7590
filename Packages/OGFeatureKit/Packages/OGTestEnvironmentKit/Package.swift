// swift-tools-version: 5.7
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
  name: "OGTestEnvironmentKit",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    // Products define the executables and libraries a package produces, and make them visible to other packages.
    .library(
      name: "OGTestEnvironmentKit",
      targets: ["OGTestEnvironmentKit"]
    )
  ],
  dependencies: [
    .package(path: "../../../OGCore"),
    .package(path: "../../../OGCore/Packages/OGAppEnvironment"),
    .package(path: "../OGFeatureCore"),
    .package(path: "../OGTenantCore"),
    .package(path: "../OGTenantKit"),
    .package(path: "../OGFeatureManager"),
    .package(path: "../../../OGExternalDependencies/OGDIService")
  ],
  targets: [
    // Targets are the basic building blocks of a package. A target can define a module or a test suite.
    // Targets can depend on other targets in this package, and on products in packages this package depends on.
    .target(
      name: "OGTestEnvironmentKit",
      dependencies: [
        "OGAppEnvironment",
        "OGCore",
        "OGFeatureCore",
        "OGDIService",
        "OGFeatureManager",
        "OGTenantKit"
      ]
    ),
    .target(
      name: "OGTestEnvironmentKitTestsUtils",
      dependencies: [
        "OGTestEnvironmentKit",
        .product(name: "OGAppEnvironmentTestsUtils", package: "OGAppEnvironment"),
        .product(name: "OGFeatureManagerTestsUtils", package: "OGFeatureManager"),
        .product(name: "OGTenantCoreTestsUtils", package: "OGTenantCore")
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "OGTestEnvironmentKitTests",
      dependencies: [
        "OGTestEnvironmentKitTestsUtils"
      ]
    )
  ]
)
