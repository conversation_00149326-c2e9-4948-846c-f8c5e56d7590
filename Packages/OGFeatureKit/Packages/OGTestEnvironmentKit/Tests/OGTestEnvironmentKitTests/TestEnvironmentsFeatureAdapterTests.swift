import Combine
import OGAppEnvironmentTestsUtils
import OGCore
import OGDIService
import OGFeatureCore
import OGFeatureManager
import OGFeatureManagerTestsUtils
import OGLogger
import OGTenantCore
import OGTenantCoreTestsUtils
import OGTenantKit
import XCTest

@testable import OGTestEnvironmentKit

// MARK: - BundledEnvironmentsFeatureFetcher

class BundledEnvironmentsFeatureFetcher: OGTestEnvironmentsFetchable {
  func fetch(identifier _: String?) throws -> OGTestEnvironment {
    try fetch()
  }

  typealias T = OGFeature

  init() {}

  func fetch() throws -> OGFeature {
    let bundledTestEnvironmentsFetcher = OGBundledTestEnvironmentsFetcher()
    return try bundledTestEnvironmentsFetcher.fetch(identifier: "de-DE") as OGFeature
  }
}

extension OGFeature {
  public init(identifier: OGIdentifier, isEnabled: Bool = true, customConfig: [String: AnyJSONType] = [:], features: Set<OGFeature> = []) {
    self.init(identifier: identifier, isEnabled: isEnabled, custom: customConfig, features: features)
  }

  public init?(identifier: String, isEnabled: Bool = true, custom: [String: AnyJSONType] = [:], features: Set<OGFeature> = []) {
    if let validIdentifier = try? OGIdentifier(identifier) {
      self.init(identifier: validIdentifier, isEnabled: isEnabled, custom: custom, features: features)
    } else {
      return nil
    }
  }
}

// MARK: - BundledEnvironmentsFeatureFetcher + OGFeatureProvidable

extension BundledEnvironmentsFeatureFetcher: OGFeatureProvidable {
  var features: Set<OGFeature> {
    var set = Set<OGFeature>()
    if let feature = try? fetch() {
      set.insert(feature)
    }
    return set
  }
}

// MARK: - TestEnvironmentsFeatureAdapterTests

final class TestEnvironmentsFeatureAdapterTests: XCTestCase {
  override func setUpWithError() throws {
    try super.setUpWithError()
    OGFeatureManagerContainer.shared.featureManger.register {
      OGFeatureManagerMock()
    }
    OGTestEnvironmentsContainer.shared.fetcherService.register {
      BundledEnvironmentsFeatureFetcher()
    }
    OGCoreContainer.shared.appEnvironment.register {
      AppEnvironmentStub(isDebugOrBetaBuild: true, isDebugBuild: true)
    }
    OGCoreContainer.shared.logger.register {
      LoggerMock()
    }
  }

  override func tearDownWithError() throws {
    OGFeatureManagerContainer.shared.featureManger.reset()
    OGCoreContainer.shared.appEnvironment.reset()
    OGCoreContainer.shared.logger.reset()
    try super.tearDownWithError()
  }

  func test_isAdaptedFeatureEnabled() throws {
    let sut = TestEnvironmentsFeatureAdapter()

    XCTAssertEqual(sut.isAdaptedFeatureEnabled, true)
  }

  func test_feature() throws {
    let sut = TestEnvironmentsFeatureAdapter()

    XCTAssertEqual(sut.feature?.identifier.value, "environments")
  }

  func test_sortedEnvironmentIdentifiers() throws {
    let sut = TestEnvironmentsFeatureAdapter()
    let tenant = try OGTenant(
      locale: Locale(identifier: "de-DE"),
      displayName: "noname",
      supportedLocales: []
    )
    OGTenantContainer.shared.publisherService().publish(new: tenant)

    let result = sut.sortedEnvironmentIdentifiers.map(\.identifier.value)

    XCTAssertEqual(result, [])
  }

  func test_testEnvironmentForIdentifier_throwsExceptionForMismatch() throws {
    let identifier = #identifier("test")

    let sut = TestEnvironmentsFeatureAdapter()

    XCTAssertThrowsError(try sut.testEnvironment(for: identifier))
  }

  func test_testEnvironmentForIdentifier_throwsExceptionForMismatchIndependantOfTenantSwitch() throws {
    let identifier = #identifier("test")

    let sut = TestEnvironmentsFeatureAdapter()

    let tenant = try OGTenant(locale: Locale(identifier: "de-DE"), displayName: "noname", supportedLocales: [])
    OGTenantContainer.shared.publisherService.callAsFunction().publish(new: tenant)
    XCTAssertThrowsError(try sut.testEnvironment(for: identifier))
  }

  func test_testEnvironmentForIdentifier_returnsTestEnvironmentIfExistsForIdentifier() throws {
    let identifier = #identifier("main")
    let expecteeOptional = OGTestEnvironment(
      identifier: "main",
      isEnabled: true,
      custom: [
        "api": AnyJSONType(jsonValue: "https://de-main.bonprix-shop.de/app-api/".jsonValue as Any),
        "web": AnyJSONType(jsonValue: "https://de-main.bonprix-shop.de".jsonValue as Any)
      ]
    )

    let sut = TestEnvironmentsFeatureAdapter()
    if let expectee = expecteeOptional {
      sut.updateCustomEnvironmentsSet([expectee])
    } else {
      XCTFail("expectee is nil")
      return
    }
    let tenant = try OGTenant(locale: Locale(identifier: "de-DE"), displayName: "noname", supportedLocales: [])
    OGTenantContainer.shared.publisherService.callAsFunction().publish(new: tenant)

    let result = try sut.testEnvironment(for: identifier)

    XCTAssertEqual(result, expecteeOptional)
  }
}
