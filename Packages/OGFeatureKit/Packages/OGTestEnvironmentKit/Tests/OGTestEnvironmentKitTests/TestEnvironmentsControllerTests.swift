import Combine
import OGAppEnvironmentTestsUtils
import OGCore
import OGDIService
import OGFeatureCore
import OGFeatureManager
import OGLogger
import OGTenantCore
import OGTenantKit
import OGTestEnvironmentKitTestsUtils
import XCTest

@testable import OGTestEnvironmentKit

// MARK: - TestEnvironmentFeatureSetPublisherMock

final class TestEnvironmentFeatureSetPublisherMock: OGTestEnvironmentFeatureSetPublishable {
  static var didTriggerPublish = false

  @Published private var testEnvironmentFeatureSetDidChange: Set<OGFeature> = []

  func publish(new testEnvironment: OGTestEnvironment?) {
    testEnvironmentFeatureSetDidChange = testEnvironment?.features ?? []
  }

  var featureSetDidChangePublisher: AnyPublisher<Set<OGFeature>, Never> {
    Self.didTriggerPublish = true
    return $testEnvironmentFeatureSetDidChange
      .compactMap { $0 }
      .eraseToAnyPublisher()
  }
}

// MARK: - CacheMock

struct CacheMock: AnyPersistable {
  static var persistedValue: Any?

  func persist(value: Any, forKey _: RawValueable) {
    Self.persistedValue = value
  }

  func value(forKey _: RawValueable) -> Any? {
    Self.persistedValue
  }

  func delete(valueForKey _: RawValueable) {
    Self.persistedValue = nil
  }

  func bool(forKey _: RawValueable) -> Bool {
    Self.persistedValue as? Bool ?? false
  }

  func persistBool(_ value: Bool, forKey _: RawValueable) {
    Self.persistedValue = value
  }
}

// MARK: - LoggerMock

class LoggerMock: OGLoggingDistributable {
  private let logSubject = PassthroughSubject<OGLog, Never>()

  public var logPublisher: AnyPublisher<OGLog, Never> {
    logSubject.eraseToAnyPublisher()
  }

  static var message = [String]()

  func log(_ log: OGLog) {
    Self.message.append(log.message)
  }
}

// MARK: - OGTestEnvironmentsServiceTests

final class OGTestEnvironmentsServiceTests: XCTestCase {
  /// Idkw but it doesn't compile otherwise..
  typealias IdentTestEnvDic = [OGIdentifier: OGTestEnvironment]

  var cancellables = Set<AnyCancellable>()

  override func setUpWithError() throws {
    try super.setUpWithError()
    reset()
    resetUnitTestRegistrations()
  }

  override func tearDownWithError() throws {
    reset()
    try super.tearDownWithError()
  }

  private func reset() {
    cancellables = []
    CacheMock.persistedValue = nil
    TestEnvironmentFeatureSetPublisherMock.didTriggerPublish = false
    LoggerMock.message = []
    OGTenantContainer.shared.selectionService.reset()
    OGTestEnvironmentsContainer.shared.testEnvironmentPersistanceService.reset()
    OGCoreContainer.shared.logger.reset()
    OGCoreContainer.shared.appEnvironment.reset()
    OGCoreContainer.shared.storage.reset()
    OGTestEnvironmentsContainer.shared.featureAdapter.reset()
  }

  private var mockedAdapter: TestEnvironmentsFeatureAdapterMock {
    OGTestEnvironmentsContainer.shared.featureAdapter.callAsFunction() as! TestEnvironmentsFeatureAdapterMock
  }

  var tenantObserverMock = TenantObserverMock()
  private func resetUnitTestRegistrations() {
    OGTenantContainer.shared.observerService.register {
      self.tenantObserverMock
    }

    OGCoreContainer.shared.logger.register {
      LoggerMock()
    }
    OGCoreContainer.shared.appEnvironment.register {
      AppEnvironmentStub(isDebugOrBetaBuild: true, isDebugBuild: true)
    }

    OGCoreContainer.shared.storage.register {
      CacheMock()
    }
    OGTestEnvironmentsContainer.shared.featureAdapter.register {
      TestEnvironmentsFeatureAdapterMock()
    }
    OGTestEnvironmentsContainer.shared.testEnvironmentFeatureSetPublisher.register {
      TestEnvironmentFeatureSetPublisherMock()
    }
  }

  func test_testEnvironment_nilIfReleaseBuildConfiguration() throws {
    OGCoreContainer.shared.appEnvironment.register {
      AppEnvironmentStub(isDebugOrBetaBuild: false, isDebugBuild: false)
    }
    let sut = OGTestEnvironmentsService()
    let result = sut.testEnvironment
    XCTAssertEqual(result, nil)
    XCTAssertTrue(LoggerMock.message.contains("testEnvironment is inaccessible due to current build configuration."))
  }

  func test_testEnvironment_nilInCaseOfDebugBuildConfigurationNoTenantAndEmptyCache() throws {
    let sut = OGTestEnvironmentsService()
    mockedAdapter.environments = []
    let result = sut.testEnvironment
    XCTAssertEqual(result, nil)
    XCTAssertTrue(LoggerMock.message.contains("TenantEnvironments did change without valid tenant."))
  }

  func test_testEnvironment_nilInCaseOfDebugBuildConfigurationAndEmptyCache() throws {
    tenantObserverMock.tenantToChangeForPublisher = try? OGTenant(
      locale: Locale(identifier: "de-DE"),
      displayName: "noname",
      supportedLocales: []
    )

    let sut = OGTestEnvironmentsService()
    mockedAdapter.environments = []
    let result = sut.testEnvironment
    XCTAssertEqual(result, nil)
    XCTAssertTrue(LoggerMock.message.contains("No cached testEnvironment"))
  }

  func test_testEnvironment_cached() throws {
    let identifier = #identifier("stage")
    let testEnv = OGTestEnvironment(identifier: identifier, isEnabled: true, custom: [:], features: [])
    let tenant = try OGTenant(
      locale: Locale(identifier: "de-DE"),
      displayName: "noname",
      supportedLocales: []
    )
    CacheMock.persistedValue = try JSONEncoder().encode([tenant.identifier: testEnv])

    tenantObserverMock.tenantToChangeForPublisher = tenant

    mockedAdapter.environments = [OGTestEnvironment(identifier: identifier)]
    let sut = OGTestEnvironmentsService()
    XCTAssertEqual(sut.testEnvironment?.identifier.value, "stage")
    let result = sut.testEnvironment
    XCTAssertEqual(result?.identifier, identifier)
  }

  func test_testEnvironment_selected() throws {
    let identifier = #identifier("stage")
    let sut = OGTestEnvironmentsService()
    try sut.selectTestEnvironment(for: identifier)
    let result = sut.testEnvironment
    XCTAssertEqual(result?.identifier, identifier)
  }

  func test_selectTestEnvironment_unknownIdentifier() throws {
    let identifier = #identifier("test")
    let sut = OGTestEnvironmentsService()
    XCTAssertThrowsError(try sut.selectTestEnvironment(for: identifier))
  }

  func test_selectTestEnvironment_knownIdentifierButUnknownTenant() throws {
    let identifier = #identifier("stage")
    let sut = OGTestEnvironmentsService()
    try sut.selectTestEnvironment(for: identifier)
    XCTAssertEqual(sut.testEnvironment?.identifier, identifier)
    XCTAssertEqual(CacheMock.persistedValue as? [String: String], nil)
  }

  func test_selectTestEnvironment_knownIdentifier() throws {
    let expectation = expectation(description: "featureSetUpdateReceived")
    let identifier = #identifier("stage")

    tenantObserverMock.tenantToChangeForPublisher = try? OGTenant(
      locale: Locale(identifier: "de-DE"),
      displayName: "noname",
      supportedLocales: []
    )

    let sut = OGTestEnvironmentsService()
    let receiver = TestEnvironmentFeatureSetPublisherMock()
    receiver.featureSetDidChangePublisher
      .sink { _ in
        TestEnvironmentFeatureSetPublisherMock.didTriggerPublish = true
        expectation.fulfill()
      }.store(in: &cancellables)
    try sut.selectTestEnvironment(for: identifier)

    guard let persistedValue = CacheMock.persistedValue as? Data else {
      XCTAssertNoThrow("TestEnvironments should be persisted as data.")
      return
    }
    let decodedPersistedValue = try JSONDecoder().decode(IdentTestEnvDic.self, from: persistedValue)
    XCTAssertEqual(sut.testEnvironment?.identifier, identifier)
    let testEnv = try OGTestEnvironment(identifier: OGIdentifier("stage"), isEnabled: true, custom: [:], features: [])
    XCTAssertEqual(decodedPersistedValue, try [OGIdentifier("de-DE"): testEnv])

    waitForExpectations(timeout: 1.3)
    XCTAssertEqual(TestEnvironmentFeatureSetPublisherMock.didTriggerPublish, true)
  }

  func test_deselectTestEnvironment_doesNotTriggerIfPreviousValueEqualsNil() throws {
    let sut = OGTestEnvironmentsService()
    sut.deselectTestEnvironment()
    XCTAssertEqual(sut.testEnvironment?.identifier, nil)
    XCTAssertEqual(CacheMock.persistedValue as? Data, nil)
    XCTAssertEqual(TestEnvironmentFeatureSetPublisherMock.didTriggerPublish, false)
  }

  func test_deselectTestEnvironment_doesTriggerIfSelectedValueExists() throws {
    let identifier = #identifier("stage")
    let expectation = expectation(description: "featureSetUpdateReceived")
    let testEnv = try OGTestEnvironment(
      identifier: OGIdentifier("stage"),
      isEnabled: true,
      custom: [:],
      features: []
    )

    CacheMock.persistedValue = try JSONEncoder().encode([OGIdentifier("de-DE"): testEnv])
    tenantObserverMock.tenantToChangeForPublisher = try? OGTenant(
      locale: Locale(identifier: "de-DE"),
      displayName: "noname",
      supportedLocales: []
    )

    mockedAdapter.environments = [OGTestEnvironment(identifier: identifier)]
    let sut = OGTestEnvironmentsService()
    XCTAssertEqual(sut.testEnvironment?.identifier.value, "stage")
    let persistedData = try JSONDecoder().decode(IdentTestEnvDic.self, from: CacheMock.persistedValue as! Data)
    XCTAssertEqual(persistedData, try [OGIdentifier("de-DE"): testEnv])

    let receiver = TestEnvironmentFeatureSetPublisherMock()
    receiver.featureSetDidChangePublisher
      .sink { _ in
        TestEnvironmentFeatureSetPublisherMock.didTriggerPublish = true
        expectation.fulfill()
      }.store(in: &cancellables)

    sut.deselectTestEnvironment()
    XCTAssertEqual(sut.testEnvironment?.identifier, nil)
    XCTAssertEqual(CacheMock.persistedValue as? [String: String], nil)

    waitForExpectations(timeout: 0.3)
    XCTAssertEqual(TestEnvironmentFeatureSetPublisherMock.didTriggerPublish, true)
  }

  func test_createTestEnvironment_addsEnvironment() throws {
    // Given
    let sut = OGTestEnvironmentsService()

    let initialEnvironmentsCount = sut.customEnvironments.value.count
    let testName = "NewTestEnvironment"
    let testUrl = AnyJSONType(jsonValue: "https://example.com")
    let testPassword = "password123"

    // When
    try sut.createTestEnvironment(name: testName, url: testUrl, password: testPassword)

    // Then
    XCTAssertEqual(sut.customEnvironments.value.count, initialEnvironmentsCount + 1, "Expected one new test environment to be added")
    let newTestEnvironment = sut.customEnvironments.value.last
    XCTAssertNotNil(newTestEnvironment, "Expected the last environment to be the newly added one")
    XCTAssertEqual(newTestEnvironment?.identifier.value, testName, "Expected the new environment to have the correct name")
  }

  func test_updateCustomEnvironment_updatesEnvironmentCorrectly() throws {
    // Given
    let sut = OGTestEnvironmentsService()

    let testName = "ExistingTestEnvironment"
    let testUrl = AnyJSONType(jsonValue: "https://oldexample.com")
    let testPassword = "oldpassword123"

    try sut.createTestEnvironment(name: testName, url: testUrl, password: testPassword)

    let updatedName = "UpdatedTestEnvironment"
    let updatedUrl = AnyJSONType(jsonValue: "https://newexample.com")
    let updatedPassword = "newpassword123"
    let environmentToUpdateIdentifier = sut.customEnvironments.value.last!.identifier

    // When
    sut.updateCustomEnvironment(name: updatedName, url: updatedUrl, password: updatedPassword, identifier: environmentToUpdateIdentifier)

    // Then
    let updatedTestEnvironment = sut.customEnvironments.value.last
    XCTAssertNotNil(updatedTestEnvironment, "Expected the last environment to be the updated one")
    XCTAssertEqual(updatedTestEnvironment?.identifier.value, updatedName, "Expected the updated environment to have the new name")
    XCTAssertEqual((updatedTestEnvironment?.features.first(where: { $0.identifier.value == "baseUrl" })?.custom["web"] as? AnyJSONType)?.jsonValue as? String, "https://newexample.com", "Expected the updated environment to have the new URL")
  }

  func test_removeCustomEnvironment_removesEnvironment() throws {
    // Given
    let sut = OGTestEnvironmentsService()

    let testName = "TestEnvironmentToRemove"
    let testUrl = AnyJSONType(jsonValue: "https://to-remove.com")
    let testPassword = "passwordToRemove"

    try sut.createTestEnvironment(name: testName, url: testUrl, password: testPassword)
    let initialEnvironmentsCount = sut.customEnvironments.value.count
    let environmentToRemoveIdentifier = sut.customEnvironments.value.last!.identifier

    // When
    sut.removeCustomEnvironment(identifier: environmentToRemoveIdentifier)

    // Then
    XCTAssertEqual(sut.customEnvironments.value.count, initialEnvironmentsCount - 1, "Expected one test environment to be removed")
    XCTAssertNil(sut.customEnvironments.value.first(where: { $0.identifier == environmentToRemoveIdentifier }), "Expected the environment with the given identifier to be removed")
  }
}
