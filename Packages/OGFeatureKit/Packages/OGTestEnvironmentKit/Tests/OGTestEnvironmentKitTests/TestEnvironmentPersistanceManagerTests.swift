import OGCore
import OGFeatureCore
import XCTest

@testable import OGTestEnvironmentKit

final class TestEnvironmentPersistanceManagerTests: XCTestCase {
  typealias IdentTestEnvDic = [OGIdentifier: OGTestEnvironment]

  override func setUpWithError() throws {
    OGCoreContainer.shared.storage.register {
      CacheMock()
    }
  }

  override func tearDownWithError() throws {
    CacheMock.persistedValue = nil
    OGCoreContainer.shared.storage.reset()
  }

  func test_persist_OGTestEnvironment() throws {
    let feature = try OGFeature(identifier: OGIdentifier("testFeature"))
    let testEnvironment = OGTestEnvironment(identifier: "test", features: [feature])
    let sut = OGTestEnvironmentPersistanceService()
    try sut.persist(
      OGTestEnvironment(
        identifier: OGIdentifier("test"),
        features: [OGFeature(identifier: OGIdentifier("testFeature"))]
      ),
      identifier: OGIdentifier("de-DE")
    )

    guard let persistedValue = CacheMock.persistedValue as? Data else {
      XCTAssertNoThrow("Data should be saved")
      return
    }

    let decodedValue = try JSONDecoder().decode(IdentTestEnvDic.self, from: persistedValue)
    XCTAssertEqual(decodedValue, try [OGIdentifier("de-DE"): testEnvironment])
  }

  func test_get_cached_testEnvironment_for_identifier() throws {
    let sut = OGTestEnvironmentPersistanceService()
    let dic = try [
      OGIdentifier("de-DE"): OGTestEnvironment(identifier: OGIdentifier("stage-ger")),
      OGIdentifier("en-EN"): OGTestEnvironment(identifier: OGIdentifier("stage-eng"))
    ]
    CacheMock.persistedValue = try JSONEncoder().encode(dic)

    let result = try sut.cached(identifier: OGIdentifier("de-DE"))
    XCTAssertEqual(result, try OGTestEnvironment(identifier: OGIdentifier("stage-ger")))
  }

  func test_dont_return_cached_testEnvironment_for_wrong_identifier() throws {
    let sut = OGTestEnvironmentPersistanceService()
    let dic = try [
      OGIdentifier("de-DE"): OGTestEnvironment(identifier: OGIdentifier("stage-ger")),
      OGIdentifier("en-EN"): OGTestEnvironment(identifier: OGIdentifier("stage-eng"))
    ]
    CacheMock.persistedValue = try JSONEncoder().encode(dic)

    let result = try sut.cached(identifier: OGIdentifier("de-de"))
    XCTAssertNil(result)
  }

  func test_remove_testEnvironment_for_identifier() throws {
    let sut = OGTestEnvironmentPersistanceService()
    let dic = try [
      OGIdentifier("de-DE"): OGTestEnvironment(identifier: OGIdentifier("stage")),
      OGIdentifier("en-EN"): OGTestEnvironment(identifier: OGIdentifier("stage"))
    ]
    CacheMock.persistedValue = try JSONEncoder().encode(dic)

    try sut.removeCachedTestEnvironment(for: OGIdentifier("de-DE"))

    guard let persistedValue = CacheMock.persistedValue as? Data else {
      XCTAssertNoThrow("Data should be saved")
      return
    }
    let decodedValue = try JSONDecoder().decode(IdentTestEnvDic.self, from: persistedValue)

    XCTAssertEqual(decodedValue, try [OGIdentifier("en-EN"): OGTestEnvironment(identifier: OGIdentifier("stage"))])
  }

  func test_delete_OGTestEnvironment() throws {
    let sut = OGTestEnvironmentPersistanceService()
    let dic = try [
      OGIdentifier("de-DE"): OGTestEnvironment(identifier: OGIdentifier("stage")),
      OGIdentifier("en-EN"): OGTestEnvironment(identifier: OGIdentifier("stage"))
    ]
    CacheMock.persistedValue = try JSONEncoder().encode(dic)

    sut.delete()

    XCTAssertNil(CacheMock.persistedValue)
  }

  func test_persistCustomEnvironments() throws {
    // Given
    let sut = OGTestEnvironmentPersistanceService()
    let testEnvironment1 = try OGTestEnvironment(identifier: OGIdentifier("stage-ger"))
    let testEnvironment2 = try OGTestEnvironment(identifier: OGIdentifier("stage-eng"))
    let customEnvironments = [testEnvironment1, testEnvironment2]

    // When
    try sut.persistCustomEnvironments(customEnvironments)

    // Then
    guard let persistedValue = CacheMock.persistedValue as? Data else {
      XCTFail("Data should be saved")
      return
    }
    let decodedValue = try JSONDecoder().decode([OGFeature].self, from: persistedValue)

    XCTAssertEqual(decodedValue, customEnvironments)
  }

  func test_loadCustomEnvironments() throws {
    // Given
    let sut = OGTestEnvironmentPersistanceService()
    let testEnvironment1 = try OGTestEnvironment(identifier: OGIdentifier("stage-ger"))
    let testEnvironment2 = try OGTestEnvironment(identifier: OGIdentifier("stage-eng"))
    let customEnvironments = [testEnvironment1, testEnvironment2]
    CacheMock.persistedValue = try JSONEncoder().encode(customEnvironments)

    // When
    let result = try sut.loadCustomEnvironments()

    // Then
    XCTAssertEqual(result, customEnvironments)
  }

  func test_deleteCustomEnvironments() throws {
    // Given
    let sut = OGTestEnvironmentPersistanceService()
    let testEnvironment1 = try OGTestEnvironment(identifier: OGIdentifier("stage-ger"))
    let testEnvironment2 = try OGTestEnvironment(identifier: OGIdentifier("stage-eng"))
    let customEnvironments = [testEnvironment1, testEnvironment2]
    CacheMock.persistedValue = try JSONEncoder().encode(customEnvironments)

    // When
    try sut.deleteCustomEnvironments()

    // Then
    XCTAssertNil(CacheMock.persistedValue)
  }

  func test_updateCustomEnvironment() throws {
    // Given
    let sut = OGTestEnvironmentPersistanceService()
    let initialTestEnvironment = try OGTestEnvironment(identifier: OGIdentifier("stage-ger"))
    let updatedTestEnvironment = try OGTestEnvironment(identifier: OGIdentifier("stage-eng"))
    let customEnvironments = [initialTestEnvironment]
    CacheMock.persistedValue = try JSONEncoder().encode(customEnvironments)

    // When
    try sut.updateCustomEnvironment(updatedTestEnvironment, identifier: OGIdentifier("stage-ger"))

    // Then
    guard let persistedValue = CacheMock.persistedValue as? Data else {
      XCTFail("Data should be saved")
      return
    }
    let decodedValue = try JSONDecoder().decode([OGFeature].self, from: persistedValue)
    XCTAssertEqual(decodedValue.first?.identifier, #identifier("stage-eng"))
  }

  func test_removeCustomEnvironment() throws {
    // Given
    let sut = OGTestEnvironmentPersistanceService()
    let testEnvironment = try OGTestEnvironment(identifier: OGIdentifier("stage-ger"))
    let customEnvironments = [testEnvironment]
    CacheMock.persistedValue = try JSONEncoder().encode(customEnvironments)

    // When
    try sut.removeCustomEnvironment(identifier: OGIdentifier("stage-ger"))

    // Then
    guard let persistedValue = CacheMock.persistedValue as? Data else {
      XCTFail("Data should be saved")
      return
    }
    let decodedValue = try JSONDecoder().decode([OGFeature].self, from: persistedValue)
    XCTAssertTrue(decodedValue.isEmpty)
  }
}
