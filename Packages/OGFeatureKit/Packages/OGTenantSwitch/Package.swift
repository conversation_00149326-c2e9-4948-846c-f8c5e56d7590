// swift-tools-version: 5.9
import PackageDescription

let package = Package(
  name: "OGTenantSwitch",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGTenantSwitch",
      targets: ["OGTenantSwitch"]
    ),
    .library(
      name: "OGTenantSwitchTestsUtils",
      targets: ["OGTenantSwitchTestsUtils"]
    )
  ],
  dependencies: [
    .package(path: "../OGBundledFeatureSetFetcher"),
    .package(path: "../../../OGCore"),
    .package(path: "../OGFeatureCore"),
    .package(path: "../OGTenantCore"),
    .package(path: "../../../OGExternalDependencies/OGDIService")
  ],
  targets: [
    .target(
      name: "OGTenantSwitch",
      dependencies: [
        "OGBundledFeatureSetFetcher",
        "OGCore",
        "OGFeatureCore",
        "OGTenantCore",
        "OGDIService"
      ]
    ),
    .target(
      name: "OGTenantSwitchTestsUtils",
      dependencies: [
        .product(name: "OGTenantCoreTestsUtils", package: "OGTenantCore"),
        "OGTenantSwitch"
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "OGTenantSwitchTests",
      dependencies: ["OGTenantSwitchTestsUtils"]
    )
  ]
)
