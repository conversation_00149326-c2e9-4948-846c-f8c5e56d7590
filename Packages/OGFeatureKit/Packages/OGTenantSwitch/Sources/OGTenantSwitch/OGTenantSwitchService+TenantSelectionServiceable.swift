import Foundation
import OGTenantCore

extension OGTenantSwitchService: OGTenantSelectionServiceable {
  public func isTenantSwitchNeeded(for url: URL) -> Bool {
    guard
      service.tenants.count > 1,
      let currentTenant = service.selectedTenant,
      let tenantFromURL = extractTenant(from: url)
    else { return false }
    return tenantFromURL.identifier != currentTenant.identifier
  }

  public func extractTenant(from url: URL) -> OGTenant? {
    guard let host = url.host else { return nil }
    return service.tenants.first(where: { tenant in
      baseUrls[tenant.identifier.value]?.contains(where: { $0 == host }) ?? false
    })
  }
}
