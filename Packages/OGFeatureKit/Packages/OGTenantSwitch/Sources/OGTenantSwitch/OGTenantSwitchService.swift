import Foundation
import OGBundledFeatureSetFetcher
import OGCore
import OGDIService
import OGFeatureCore
import OGTenantCore
import OGTenantKit

// MARK: - DispatchQueueType

public protocol DispatchQueueType {
  func async(execute work: @escaping @convention(block) () -> Void)
}

// MARK: - DispatchQueue + DispatchQueueType

extension DispatchQueue: DispatchQueueType {
  public func async(execute work: @escaping @convention(block) () -> Void) {
    async(group: nil, qos: .unspecified, flags: [], execute: work)
  }
}

// MARK: - OGTenantSwitchService

public final class OGTenantSwitchService {
  @OGInjected(\OGCoreContainer.logger) private var logger
  @OGInjected(\OGTenantContainer.service) internal var service
  @OGInjected(\OGFeatureSetContainer.featureSetBundleFetcherService) private var featureSetBundleFetcherService
  private let dispatchQueue: DispatchQueueType
  private let customKey: String
  private let featureIdentifier: OGIdentifier
  internal var baseUrls: [String: [String]] = [:]

  public init(featureIdentifier: String = "baseUrl", customKey: String = "web", dispatchQueue: DispatchQueueType = DispatchQueue.global(qos: .utility)) throws {
    self.featureIdentifier = try OGIdentifier(featureIdentifier)
    self.customKey = customKey
    self.dispatchQueue = dispatchQueue
    loadAllBaseUrlFeatures()
  }

  private func loadAllBaseUrlFeatures() {
    dispatchQueue.async { [weak self] in
      guard let self else { return }
      do {
        var baseUrls: [String: [String]] = [:]
        for tenant in self.service.tenants {
          let featureSet = try self.featureSetBundleFetcherService.fetch(forIdentifier: tenant.identifier)
          let feature = try featureSet.features.element(for: self.featureIdentifier)
          if let url: URL = feature.customValue(for: self.customKey) {
            guard let host = url.host else { continue }
            baseUrls[tenant.identifier.value] = [host]
          } else if let urls: [URL] = feature.customValue(for: self.customKey) {
            let hosts = urls.compactMap(\.host)
            baseUrls[tenant.identifier.value] = hosts
          }
        }
        self.baseUrls = baseUrls
      } catch {
        self.logger.log(.warning, domain: .service, message: error.localizedDescription)
      }
    }
  }
}
