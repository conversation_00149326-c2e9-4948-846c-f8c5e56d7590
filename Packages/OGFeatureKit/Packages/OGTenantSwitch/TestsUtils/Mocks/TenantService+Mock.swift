import Combine
import Foundation
import OGIdentifier
import OGTenantCore
import OGTenantCoreTestsUtils
import OGTenantKit

public class TenantServiceMock: OGTenantServiceable {
  @Published var tenant: OGTenant?
  public var selectedTenant: OGTenant?

  public var selectedTenantPublished: Published<OGTenant?>.Publisher { $tenant }

  public init() {}

  public var tenants: [OGTenant] = [
    try! OGTenant(locale: Locale(identifier: "de-DE")),
    try! OGTenant(locale: Locale(identifier: "fr-FR")),
    try! OGTenant(locale: Locale(identifier: "nl-NL"))
  ]

  public func setTenant(for identifier: OGIdentifier) throws {
    selectedTenant = tenants.first(where: { $0.identifier.value == identifier.value })
  }
}
