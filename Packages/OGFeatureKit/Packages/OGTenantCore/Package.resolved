{"pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "194a6706acbd25e4ef639bcaddea16e8758a3e27", "version": "1.2024011602.0"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "3b62f154d00019ae29a71e9738800bb6f18b236d", "version": "10.19.2"}}, {"identity": "factory", "kind": "remoteSourceControl", "location": "https://github.com/hmlongco/Factory", "state": {"revision": "061b3afe0358a0da7ce568f8272c847910be3dd7", "version": "2.2.0"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk.git", "state": {"revision": "42eae77a0af79e9c3f41df04a23c76f05cfdda77", "version": "10.24.0"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "51ba746a9d51a4bd0774b68499b0c73ef6e8570d", "version": "10.24.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "a637d318ae7ae246b02d7305121275bc75ed5565", "version": "9.4.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "57a1d307f42df690fdef2637f3e5b776da02aad6", "version": "7.13.3"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "e9fad491d0673bdda7063a0341fb6b47a30c5359", "version": "1.62.2"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "a2ab612cb980066ee56d90d60d8462992c07f24b", "version": "3.5.0"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "2d12673670417654f08f5f90fdd62926dc3a2648", "version": "100.0.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "og-dx_aac-multiplatform-sdk", "kind": "remoteSourceControl", "location": "https://github.com/aacml/og-dx_aac-multiplatform-sdk.git", "state": {"revision": "54d5a6ccaf061e90cc95af84c12137c94d5b2fda", "version": "4.19.7"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "snowplow-ios-tracker", "kind": "remoteSourceControl", "location": "https://github.com/snowplow/snowplow-ios-tracker", "state": {"revision": "20b1fea9c58334e569cb63d71875d3c2d0243483", "version": "6.0.7"}}, {"identity": "swift-concurrency-extras", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-concurrency-extras", "state": {"revision": "bb5059bde9022d69ac516803f4f227d8ac967f71", "version": "1.1.0"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "102a647b573f60f73afdce5613a51d71349fe507", "version": "1.30.0"}}, {"identity": "swift-syntax", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-syntax.git", "state": {"revision": "6ad4ea24b01559dde0773e3d091f1b9e36175036", "version": "509.0.2"}}, {"identity": "ziparchive", "kind": "remoteSourceControl", "location": "https://github.com/ZipArchive/ZipArchive.git", "state": {"revision": "38e0ce0598e06b034271f296a8e15b149c91aa19", "version": "2.4.3"}}], "version": 2}