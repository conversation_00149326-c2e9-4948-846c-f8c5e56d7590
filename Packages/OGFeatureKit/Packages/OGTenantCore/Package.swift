// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "OGTenantCore",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGTenantCore",
      targets: ["OGTenantCore"]
    ),
    .library(
      name: "OGTenantCoreTestsUtils",
      targets: ["OGTenantCoreTestsUtils"]
    )
  ],
  dependencies: [
    .package(path: "../../../OGCore"),
    .package(path: "../OGFeatureCore")
  ],
  targets: [
    .target(
      name: "OGTenantCore",
      dependencies: [
        "OGCore",
        "OGFeatureCore"
      ]
    ),
    .target(
      name: "OGTenantCoreTestsUtils",
      dependencies: [
        "OGTenantCore"
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "OGTenantCoreTests",
      dependencies: ["OGTenantCoreTestsUtils"]
    )
  ]
)
