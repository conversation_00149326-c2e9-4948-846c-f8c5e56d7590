@testable import OGTenantCore
import XCTest

final class ISO3166Alpha2CountryCodeTests: XCTestCase {
  func testInitilizationForTwoCapitalizedLetters() throws {
    XCTAssertNoThrow(try ISO3166Alpha2CountryCode("DE"))
    XCTAssertNoThrow(try ISO3166Alpha2CountryCode("FR"))
    XCTAssertNoThrow(try ISO3166Alpha2CountryCode("RO"))
    XCTAssertNoThrow(try ISO3166Alpha2CountryCode("US"))
    XCTAssertNoThrow(try ISO3166Alpha2CountryCode("GB"))
  }

  func testInitilizationThrowsForTwoLowercasedLetters() throws {
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("de"))
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("fr"))
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("ro"))
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("us"))
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("gb"))
  }

  func testInitilizationThrowsForOneCapitalizedLetterAndOneNumber() throws {
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("D1"))
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("F2"))
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("R3"))
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("U4"))
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("G5"))
  }

  func testInitilizationThrowsForOneLowercasedLetterAndOneNumber() throws {
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("d1"))
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("f2"))
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("3r"))
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("u4"))
    XCTAssertThrowsError(try ISO3166Alpha2CountryCode("g5"))
  }
}
