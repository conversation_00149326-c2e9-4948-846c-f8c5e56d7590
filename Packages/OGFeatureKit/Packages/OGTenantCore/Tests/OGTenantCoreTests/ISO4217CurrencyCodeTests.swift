@testable import OGTenantCore
import XCTest

final class ISO4217CurrencyCodeTests: XCTestCase {
  func testInitilizationForThreeCapitalizedLetters() throws {
    XCTAssertNoThrow(try ISO4217CurrencyCode("EUR"))
    XCTAssertNoThrow(try ISO4217CurrencyCode("USD"))
    XCTAssertNoThrow(try ISO4217CurrencyCode("JPY"))
    XCTAssertNoThrow(try ISO4217CurrencyCode("GBP"))
    XCTAssertNoThrow(try ISO4217CurrencyCode("CHF"))
  }

  func testInitilizationThrowsForThreeLowercasedLetters() throws {
    XCTAssertThrowsError(try ISO4217CurrencyCode("eur"))
    XCTAssertThrowsError(try ISO4217CurrencyCode("usd"))
    XCTAssertThrowsError(try ISO4217CurrencyCode("jpy"))
    XCTAssertThrowsError(try ISO4217CurrencyCode("gbp"))
    XCTAssertThrowsError(try ISO4217CurrencyCode("chf"))
  }

  func testInitilizationThrowsForOneCapitalizedLetterAndOneLowercasedAndOneNumber() throws {
    XCTAssertThrowsError(try ISO4217CurrencyCode("Eu1"))
    XCTAssertThrowsError(try ISO4217CurrencyCode("Us2"))
    XCTAssertThrowsError(try ISO4217CurrencyCode("Jp3"))
    XCTAssertThrowsError(try ISO4217CurrencyCode("Gb4"))
    XCTAssertThrowsError(try ISO4217CurrencyCode("Ch5"))
  }

  func testInitilizationThrowsForOneTwoLowercasedLettersAndOneNumber() throws {
    XCTAssertThrowsError(try ISO4217CurrencyCode("eu1"))
    XCTAssertThrowsError(try ISO4217CurrencyCode("us2"))
    XCTAssertThrowsError(try ISO4217CurrencyCode("jpr"))
    XCTAssertThrowsError(try ISO4217CurrencyCode("gb4"))
    XCTAssertThrowsError(try ISO4217CurrencyCode("ch5"))
  }
}
