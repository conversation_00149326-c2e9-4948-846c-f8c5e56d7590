@testable import OGTenantCore
import XCTest

final class ISO639_1LanguageCodeTests: XCTestCase {
  func testInitilizationForTwoLowercasedLetters() throws {
    XCTAssertNoThrow(try ISO639_1LanguageCode("de"))
    XCTAssertNoThrow(try ISO639_1LanguageCode("fr"))
    XCTAssertNoThrow(try ISO639_1LanguageCode("ro"))
    XCTAssertNoThrow(try ISO639_1LanguageCode("us"))
    XCTAssertNoThrow(try ISO639_1LanguageCode("gb"))
  }

  func testInitilizationThrowsForTwoCapitalizedLetters() throws {
    XCTAssertThrowsError(try ISO639_1LanguageCode("DE"))
    XCTAssertThrowsError(try ISO639_1LanguageCode("FR"))
    XCTAssertThrowsError(try ISO639_1LanguageCode("RO"))
    XCTAssertThrowsError(try ISO639_1LanguageCode("US"))
    XCTAssertThrowsError(try ISO639_1LanguageCode("GB"))
  }

  func testInitilizationThrowsForOneCapitalizedLetterAndOneNumber() throws {
    XCTAssertThrowsError(try ISO639_1LanguageCode("D1"))
    XCTAssertThrowsError(try ISO639_1LanguageCode("F2"))
    XCTAssertThrowsError(try ISO639_1LanguageCode("R3"))
    XCTAssertThrowsError(try ISO639_1LanguageCode("U4"))
    XCTAssertThrowsError(try ISO639_1LanguageCode("G5"))
  }

  func testInitilizationThrowsForOneLowercasedLetterAndOneNumber() throws {
    XCTAssertThrowsError(try ISO639_1LanguageCode("d1"))
    XCTAssertThrowsError(try ISO639_1LanguageCode("f2"))
    XCTAssertThrowsError(try ISO639_1LanguageCode("3r"))
    XCTAssertThrowsError(try ISO639_1LanguageCode("u4"))
    XCTAssertThrowsError(try ISO639_1LanguageCode("g5"))
  }
}
