import Foundation
import OGIdentifier
import OGTenantCore

extension OGTenant {
  public init(
    locale: Locale = Locale(identifier: "de-DE"),
    pushId _: UInt = 1,
    displayName: String = "Tenant",
    localeSpecificName _: String = "Mandant",
    supportedLocales: [String] = []
  ) throws {
    try self.init(
      identifier: OGIdentifier(locale.identifier),
      locale: locale,
      supportedLocales: supportedLocales.compactMap { Locale(identifier: $0) },
      displayName: displayName,
      features: [],
      isDefault: false,
      currencyCode: ISO4217CurrencyCode(locale.currencyCode!),
      languageCode: ISO639_1LanguageCode(locale.languageCode!),
      countryCode: ISO3166Alpha2CountryCode(locale.regionCode!)
    )
  }
}
