import OGIdentifier
import OGMacros
import OGTenantCore

extension OGTenant {
  public static let germany: OGTenant = try! OGTenant(identifier: #identifier("de-DE"), displayName: "Deutschland", features: [], supportedLocales: [], isDefault: true)
  public static let austria: OGTenant = try! OGTenant(identifier: #identifier("at-DE"), displayName: "Österreich", features: [], supportedLocales: [], isDefault: false)
  public static let spain: OGTenant = try! OGTenant(identifier: #identifier("es-ES"), displayName: "España", features: [], supportedLocales: [], isDefault: false)
  public static let sweden: OGTenant = try! OGTenant(identifier: #identifier("sv-SE"), displayName: "Sverige", features: [], supportedLocales: [], isDefault: false)
  public static let switzerlandFR: OGTenant = try! OGTenant(identifier: #identifier("fr-CH"), displayName: "Suisse", features: [], supportedLocales: [], isDefault: false)
  public static let switzerlandDE: OGTenant = try! OGTenant(identifier: #identifier("de-CH"), displayName: "Schweiz", features: [], supportedLocales: [], isDefault: false)
  public static let stub: OGTenant = germany
}

extension [OGTenant] {
  public static let stub: [OGTenant] = [.germany, .austria, .spain, .sweden]
}
