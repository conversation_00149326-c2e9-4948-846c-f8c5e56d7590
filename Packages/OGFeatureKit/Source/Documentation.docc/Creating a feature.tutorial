@Tutorial(time: 10) {
  @Intro(title: "Creating a feature") {
    In this tutorial, you will learn how to setup the a JSON file that contains a list of all your features.
    @Image(source: OGKit.png, alt: "")
  }
  
  
  @Section(title: "Creating a feature") {
    @ContentAndMedia {
      In this tutorial, we will guide you through the process of creating a JSON configuration file for your app. This file will contain a comprehensive list of all the feature combinations that your app requires. By creating a config file, you can easily manage and organize the various components and functionalities of your app in one place. We will walk you through each step to ensure that you are able to successfully set up your config file. 
      @Image(source: OGKit.png, alt: "")
    }
    
    @Steps {
      @Step {
        Create a new JSON file. The naming of the JSON file needs to conform [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1) as the language code, following a dash and [ISO 3166-1](https://en.wikipedia.org/wiki/ISO_3166-1) for the country code. Examples include: _de-DE, en-US, fr-FR, es-ES_. In this tutorial, we will use a config for Germany, _"de-DE"_.
        
        
      }
      
      @Step {
        Create a new JSON file named `de-DE.json` and add it to your main bundle.
        
        @Code(name: "de-DE.json", file: config_0.json) 
      }
      
      @Step {
        The `features` parameter is a JSON object that holds a list of features. 
        
        @Code(name: "de-DE.json", file: config_1.json) 
      }    
      
      @Step {
        A basic feature consists of an `identifier` that has to be **unique** per config and the parameter `isEnabled` that indicates wether a feature is enabled or not.
        
        @Code(name: "de-DE.json", file: config_2.json) 
      }    
      
      @Step {
        Let's add another feature.
        
        @Code(name: "de-DE.json", file: config_3.json) 
      }    
      
      @Step {
        A feature can also contain an optional parameter `custom`. Said parameter stores another JSON object (key-value pairs). It can hold: `Strings`, `Numbers`, `Booleans`, `Arrays` and other objects.
        
        @Code(name: "de-DE.json", file: config_4.json)
      } 
      
      @Step {
        If you need another config, e.g. the Spanish version of your app. Add another config under the name `es-ES.json` and change the according values. 
        
        @Code(name: "es-ES.json", file: config_5.json)
      }    
    }
  }
}
