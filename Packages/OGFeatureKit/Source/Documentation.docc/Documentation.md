# OGFeatureKit
The OGFeatureKit provides feature management capabilities for iOS apps. It allows you to configure features based on tenants, customize them for different locales, and test various scenarios. This documentation provides an overview of the key concepts and usage instructions for the `OGFeatureKit`.

## Overview

- **Tenant**

An Tenant represents a specific configuration for an country entity within the app. Each tenant can have its own set of features and customizations. It provides a way to manage distinct app configurations for different entities.

- **Feature**

A Feature refers to a specific functionality or capability within the app. It represents a distinct feature that can be enabled or disabled based on the configuration of a tenant. Each feature has a unique identifier and can have additional custom properties associated with it.

- **Test Environment**

A Test Environment allows you to configure app features for specific test scenarios. It provides a way to customize feature sets for different environments within a tenant. By utilizing test environments, you can test and control the availability of features based on various factors.

## Examples

### Create a Tenant file
Create a JSON configuration file named "de-DE.json" for the tenant and add it to your app target. The naming of the JSON file needs to conform [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1) as the language code, following a dash and [ISO 3166-1](https://en.wikipedia.org/wiki/ISO_3166-1) for the country code. Examples include: _de-DE, en-US, fr-FR, es-ES_. In this tutorial, we will use a config for Germany, _"de-DE"_.

### Configure a Tenant
Configure the JSON file for the tenant, specifying its properties and supported locales.

```json
{
 "displayName": "Deutschland",
 "isDefault": true,
 "supportedLocales": ["de-DE"],
}
```

- **displayName** 

This field represents the display name or human-readable name of the tenant. In this case, the tenant is named "Deutschland" (Germany).
- **isDefault** 

This field indicates whether the current tenant is the default one. If set to true, it means that this tenant is the default configuration for the app.
- **supportedLocales**

This field defines the supported locales for the tenant. In this case, the "de-DE" locale is specified, indicating that this tenant is associated with the German locale.
To support different languages within a tenant, you can specify the supported locales for the tenant. This allows you to define specific configurations for each locale. To support multiple languages, you can specify the language code without the region in the supportedLocales array. If you want to support all regions for a language, you can specify the region code in uppercase.
```json
"supportedLocales": ["EN"]
```

### Creating Features for a Tenant
Add a field that contains an array of feature configurations for the tenant. 
Each feature is represented as an object with the following properties:
```json
{
 "displayName": "Deutschland",
 "isDefault": true,
 "supportedLocales": ["de-DE"],
 "features": [
 {
  "identifier": "showBouncer",
  "isEnabled": false
 },
 {
  "identifier": "foo",
  "isEnabled": true,
	"custom": {
   "privacyTitle": "Privacidad",
   "privacyUrl": "https://www.someUrl.es",
   "names": ["John Appleseed", "Tim Cook", "Steve Jobs", "Craig Federighi"],
   "isItTrue": true,
   "luckyNumber": 7
  }}]
}
```
- **identifier**

This field identifies the feature. It serves as a unique identifier for the feature within the app.

- **isEnabled**

This field determines whether the feature is enabled or disabled for the tenant. In this example, the "showBouncer" feature is disabled (false), while the "foo" feature is enabled (true).
- **custom**

This field is optional and can hold additional custom data associated with the feature. In this case, the "foo" feature has custom properties such as a privacy title, privacy URL, a list of names, a boolean value, and a lucky number.


**Note:**
To create a feature for a tenant, consider the following:
Define a unique identifier for the feature. This identifier will be used to enable or disable the feature in the tenant configuration.
Determine whether the feature should be enabled or disabled by default for the tenant.
Optionally, you can add custom properties to the feature using the custom field in the JSON configuration. These custom properties can be used to provide additional information or customization options for the feature.

### Initializing the OGFeatureKit
Inject a new instance of the `OGFeatureManager` into the `OGFeatureManagerContainer` during app startup. This will allow you to register and manage your app's features.
```swift
import OGFeatureKit
let featureManager = OGFeatureManager(defaultProvider: OGBundledFeatureSetFetcherContainer.shared.bundledFeatureSetFetcher())
		 
OGFeatureManagerContainer.shared.featureManger.register {
 featureManager
}
```

### Accessing a Feature

To access a feature in your app using the OGFeatureKit, you need to create a subclass of OGFeatureAdapter and customize it according to your specific needs. 
```swift
import OGFeatureKit
class BouncerFeatureAdapter: OGFeatureAdapter {
  override public class var featureName: OGFeature.Name { "showBouncer" }
  @Published private var isEnabled: Bool = false
	
  private var cancellables = Set<AnyCancellable>()
	
  override public init() {
   super.init()
   receiveUpdates()
  }
 
  private func receiveUpdates() {
   $feature.sink { [weak self] feature in
    self?.isEnabled = feature?.isEnabled ?? false
   }.store(in: &cancellables)
  }
}
```
To access a feature using a custom feature adapter class, follow these steps:

- Create a new file in your project and name it based on the identifier of the feature you wish to retrieve, suffixed with "FeatureAdapter". For example, if the feature identifier is "bouncer", name the file "BouncerFeatureAdapter".
- Import the OGFeatureKit package and declare that the adapter class should inherit from OGFeatureAdapter.
- Override the featureName class variable and specify the identifier of the desired feature.
- In the initializer of your adapter class, call super.init() to initialize the superclass.
- Add any properties you want to publish, such as @Published properties for feature state.
- Import Combine framework to make use of the sink method on the feature object. This allows you to access and assign the necessary properties from the published features. Store the sink in the cancellables set to manage the subscriptions.

### Accessing Custom Properties from the Feature
To retrieve custom values of a specific type from a feature using the OGFeatureKit, you can utilize the customValue function provided by OGFeature. Here's an example of accessing a custom property using a key:
```swift
let privacyTitle: String = feature.customValue(for: "privacyTitle")
```
By calling customValue with the desired key, you can retrieve custom values associated with a feature.

### Accessing Tenants
The `OGTenantService` class manages tenant selection and provides information about available tenants.

#### Fetching a List of Tenants
To fetch a list of tenants, you can use the tenants property of the tenantService object. This property returns an array of OGTenant objects, which represent the tenants in the system.

To obtain a reference to the tenantService object, you can use the @OGInjected property wrapper and inject the service using the OGTenantContainer.service key.

```swift
import OGFeatureKit
class ViewModel {
 @OGInjected(\OGTenantContainer.service) private var tenantService

 func fetchTenants() {
  let tenants = tenantService.tenants  // Fetch the list of tenants
  print(tenants)  // Outputs the list of tenants
 }
}
```
- Import the OGFeatureKit package.
- Inject the `OGTenantService`.
- Access all available Tenants though the `tenants` property.  

#### Listening for Tenant Changes

To listen for tenant changes, you can use the selectedTenantPublished publisher of the tenantService object. This publisher emits the currently selected tenant whenever it changes.

To listen for tenant changes, you can subscribe to the selectedTenantPublished publisher and update your code accordingly.

```swift
import OGFeatureKit
import Combine
class ViewModel {
 @OGInjected(OGTenantContainer.service) private var tenantService: OGTenantServicing
 private var tenantChangeSubscriptions = Set<AnyCancellable>()
 func listenForTenantChanges() {
   tenantService.selectedTenantPublished
    .sink { tenant in
     guard let tenant = tenant else { return }
     print("Selected tenant has changed to:", tenant)
     // Update your code as needed
    }
    .store(in: &tenantChangeSubscriptions)
}
```
- Import the OGFeatureKit package.
- Inject the `OGTenantService`.
- Import Combine framework to make use of the sink method on the `selectedTenantPublished` object. This allows you to access and assign the necessary properties from the published Tenants. Store the sink in the cancellables set to manage the subscriptions.


#### Selecting a Tenant

To select a tenant, you can use the setTenant(for:) method of the tenantService object. This method sets the tenant with the specified identifier as the selected tenant.

To call the setTenant(for:) method, you can use the tenantService object obtained as described above.
```swift
import OGFeatureKit
class ViewModel {
 @OGInjected(OGTenantContainer.service) private var tenantService: OGTenantServicing

 func selectTenant(withIdentifier tenantIdentifier: OGIdentifier) throws {
  try tenantService.setTenant(for: tenantIdentifier)
 }
}
```
- Import the OGFeatureKit package.
- Inject the `OGTenantService`.
- Call the `setTenant(for: OGIdentifier)` function to set the selected Tenant.

### Creating Test Environments

The `OGTestEnvironmentKit` is a framework for managing `OGTestEnvironment` objects in an iOS app. The OGTestEnvironment objects represent sets of features that can be enabled or disabled for testing purposes.

The `OGTestEnvironmentsService` is the main entry point, that allows to select and deselect OGTestEnvironment objects, and provides access to the selected OGTestEnvironment object. It also receives updates about the available `OGTestEnvironment` objects, and persists the selected OGTestEnvironment object in the local cache.
 
```json
{
  "features": [{
   "identifier": "baseUrl",
   "custom": {
    "api": "https://test.otto.de/app-api/",
    "web": "https://test.otto.de"
    }
   }
 ]
}
```

- Create a new JSON file named `de-DE_test.json` and add it as a target dependency to your project.
The `test` suffix is the identifier of the test environment and will be used as the display name later.
Note: The `de-DE` prefix must exist as a tenant (see the OGTenantKit Documentation for more details). 
- Add an array called `features` to the object. Thats the array of features that will be used, if this test environment is selected.
- Add an feature object to the array (e.g. an new base url feature). A feature in this array will override the release feature, if the test environment is selected.
- Create additional test environment JSON files (e.g de-DE_beta) and add them as a target dependency to your project. 

### Injecting the OGFeatureKit
Inject a new instance of the OGFeatureManager into the OGFeatureManagerContainer during app startup. This will allow you to register and manage your app’s features.
```swift
import OGFeatureKit
let featureManager = OGFeatureManager(defaultProvider: OGBundledFeatureSetFetcherContainer.shared.bundledFeatureSetFetcher())
		 
OGFeatureManagerContainer.shared.featureManager.register {
 featureManager
}```

### Accessing Test Environments

The service is intended to be used in a debug or beta build of the app, as the testEnvironment property and the selectTestEnvironment(for:) and deselectTestEnvironment() methods are only accessible in these build configurations. In other build configurations, the testEnvironment property returns nil, and the selectTestEnvironment(for:) and deselectTestEnvironment() methods do not have any effect. 

#### Fetching a List of Test Environments
To fetch a list of test environments, you can use the `environments` property of the `testEnvironmentsService? object. This property returns an array of `OGTestEnvironment` objects, which represent the Test Environments in the system.

To obtain a reference to the `testEnvironmentsService` object, you can use the @OGInjected property wrapper and inject the service using the OGTestEnvironmentsContainer.testEnvironmentsService key.
```swift
Import the OGFeatureKit
class ViewModel: ObservableObject {
	
 @OGInjected(\OGTestEnvironmentsContainer.testEnvironmentsService) private var testEnvironmentsService
	
 func fetchTestEnvironments() {
  let testEnvironments = testEnvironmentsService.environments
  print(testEnvironments)
  let isRelease = testEnvironmentsService.testEnvironment == nil
  print(isRelease)
 }
}
```
- Import the OGFeatureKit package.
- Inject the `OGTestEnvironmentsService`.
- Get all available test environments through the `environments' property.  
- Get the selected test environment through the `testEnvironment` property. if the `testEnvironment` is `nil`, no test environment is selected, which means that your application is using the release configuration.

#### Selecting a Test Environment
To select a test environment, you can use the selectTestEnvironment(for:) method of the testEnvironmentsService object. This method sets the environment with the specified identifier as the selected test environment.

To call the setTenant(for:) method, you can use the tenantService object obtained as described above.
```swift
Import the OGFeatureKit
class ViewModel: ObservableObject {
	
 @OGInjected(\OGTestEnvironmentsContainer.testEnvironmentsService) private var testEnvironmentsService

 func onTestEnvironmentSelect(_ testEnvironment: Model) {
	guard let identifier = testEnvironmentsService.environments
	 .first(where: { testEnvironment.id == $0.identifier.value })?.identifier else { return }
	try? testEnvironmentsService.selectTestEnvironment(for: identifier)
	fetchTestEnvironments()
 }
}
```
- Import the OGFeatureKit package.
- Inject the `OGTestEnvironmentsService`.
- Call the `selectTestEnvironment(for: OGIdentifier)` function to set the selected Test Environment.

#### Deselecting a Test Environment
To deselect a test environment, you can use the `deselectTestEnvironment()` method of the testEnvironmentsService object. This method deselects the Test Environment and publishes the release environment.
```swift
Import the OGFeatureKit
class ViewModel: ObservableObject {

 @OGInjected(\OGTestEnvironmentsContainer.testEnvironmentsService) private var testEnvironmentsService
	
 func deselectTestEnvironment() {
	testEnvironmentsService.deselectTestEnvironment()
	fetchTestEnvironments()
 }
}
```
- Import the OGFeatureKit package.
- Inject the `OGTestEnvironmentsService`.
- Call the `deselectTestEnvironment()` function to deselect the selected Test Environment and publish the release environment.
