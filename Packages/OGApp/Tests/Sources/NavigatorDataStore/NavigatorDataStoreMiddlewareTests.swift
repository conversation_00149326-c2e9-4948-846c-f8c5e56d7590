import OGAppTestsUtils
import OGBadge
import OGBadgeTestsUtils
import OGCore
import OGFeatureAdapterTestsUtils
import OGFeatureKit
import OGNavigationCoreTestsUtils
import OGTrackerOptInServiceTestsUtils
import XCTest
@testable import OGApp

// MARK: - NavigatorDataStoreMiddlewareTests

final class NavigatorDataStoreMiddlewareTests: XCTestCase {
  // MARK: NavigatorDataAction._setShowTabBarBadge Tests

  func test_WHEN_setOnboardingFinished_THEN_return_nil() async throws {
    let sut = NavigatorDataState.Middleware()
    let nextEvent = await sut.callAsFunction(
      action: ._setShowTabBarBadge(true),
      for: .initial
    )

    XCTAssertNil(nextEvent)
  }

  func test_WHEN_setOnboardingFinished_THEN_dispatch_showTabBarBadge() async throws {
    let badgeStore = OGBadgeStore.mock
    let sut = NavigatorDataState.Middleware(badgeStore: badgeStore)
    _ = await sut.callAsFunction(
      action: ._setShowTabBarBadge(true),
      for: .initial
    )

    XCTAssertEqual(badgeStore.dispatchDetachedCallCount, 1)
    XCTAssertEqual(
      badgeStore.dispatchDetachedArg,
      OGBadgeAction.showTabBarBadge(true)
    )
  }

  // MARK: NavigatorDataAction._setNavigatorData Tests

  func test_WHEN_setNavigatorData_THEN_return_registerBadge() async throws {
    let sut = NavigatorDataState.Middleware()
    let nextEvent = await sut.callAsFunction(
      action: ._setNavigatorData(.init()),
      for: .initial
    )

    XCTAssertEqual(nextEvent, ._registerBadge(.init()))
  }

  func test_WHEN_setNavigatorData_AND_contains_account_tab_AND_showTabBarBadge_true_THEN_dispatch_setTab() async throws {
    let badgeStore = OGBadgeStore.mock
    let sut = NavigatorDataState.Middleware(badgeStore: badgeStore)
    _ = await sut.callAsFunction(
      action: ._setNavigatorData(.accountNavigatorDataStub),
      for: .init(showTabBarBadge: true)
    )

    XCTAssertEqual(badgeStore.dispatchDetachedCallCount, 1)
    XCTAssertEqual(
      badgeStore.dispatchDetachedArg,
      OGBadgeAction.setTab(tab: .accountStub, badgeContent: "✓")
    )
  }

  func test_WHEN_setNavigatorData_AND_showTabBarBadge_false_THEN_nothing() async throws {
    let badgeStore = OGBadgeStore.mock
    let sut = NavigatorDataState.Middleware(badgeStore: badgeStore)
    _ = await sut.callAsFunction(
      action: ._setNavigatorData(.accountNavigatorDataStub),
      for: .init(showTabBarBadge: false)
    )

    XCTAssertEqual(badgeStore.dispatchDetachedCallCount, 0)
  }
}
