import Account
import AdjustSdk
import AirshipCore
import AppCore
import AppTracker
import Assortment
import BraFittingGuide
import CatalogScanner
import Combine
import Deals
import FirebaseCore
import Inbox
import Login
import NativeAPI
import OGAdjustReporter
import OGAirshipReporter
import OGAppLifecycle
import OGCore
import OGDetective
import OGDetectiveComponent
import OGDialogCoordinator
import OGExternalBrowser
import OGFeatureKit
import OGFirebaseKit
import OGFirebaseReporter
import OGInAppBrowser
import OGL10n
import OGLogger
import OGNavigation
import OGSecret
import OGSystemKit
import OGTracker
import OGURLCredentialStorage
import OGUserAgent
import OGUserCore
import OGWebBridge
import OGWebBridgeTracker
import OGWebView
import ProductDetail
import ProductReviewDetail
import PromoBanner
import PushPromotion
import Recommendation
import Review
import Salutation
import Search
import StoreFinder
import TenantChooser
import TenantSwitch
import UICatalog
import Welcome

// MARK: - AppConfigurator

public final class AppConfigurator {
  private(set) var receivables: [OGLogReceivable] = []
  private var subscription: Set<AnyCancellable> = []
  public init(
    credentialStorage: OGURLCredentialStoring? = nil,
    fontResolver: OGFontResolvable,
    secrets: [OGSecret],
    tenantService: OGTenantServiceable? = nil,
    isDebugOrBetaBuild: Bool = false,
    isDebugBuild: Bool = false
  ) {
    registerSecretService(secrets: secrets)
    registerFirebase()
    registerStores()

    setupDependencies(
      credentialStorage: credentialStorage,
      fontResolver: fontResolver,
      tenantService: tenantService,
      isDebugOrBetaBuild: isDebugOrBetaBuild,
      isDebugBuild: isDebugBuild
    )
    registerUserAgent()
    setupTheme()
  }

  private func registerUserAgent() {
    let adjustHandler = OGAdjustReporterContainer.shared.adjustHandler()
    adjustHandler.isInitialized
      .removeDuplicates()
      .sink { isInitialized in
        guard isInitialized else { return }
        Task.detached {
          var dynamicValuesProvider = OGUserAgentContainer.shared.dynamicValuesProvider()
          let adjustId = await adjustHandler.getAdjustId()
          dynamicValuesProvider.adjustId = {
            adjustId
          }

          dynamicValuesProvider.airshipChannelId = {
            Airship.isFlying ? Airship.channel.identifier : nil
          }
        }
      }.store(in: &subscription)
  }

  private func setupDependencies(
    credentialStorage: OGURLCredentialStoring?,
    fontResolver: OGFontResolvable,
    tenantService: OGTenantServiceable?,
    isDebugOrBetaBuild: Bool = false,
    isDebugBuild: Bool = false
  ) {
    let appEnvironment = registerAppEnvironment(
      isDebugOrBetaBuild: isDebugOrBetaBuild,
      isDebugBuild: isDebugBuild
    )
    registerTrackingReporters()
    registerRouteProvider()
    registerDestinationProviders(appEnvironment: appEnvironment)
    registerTenantService(tenantService: tenantService)
    registerLoggers(isDebugBuild: isDebugBuild)
    registerFeatureManager()
    registerDetective(isDebugOrBetaBuild: isDebugOrBetaBuild)
    registerFontResolver(fontResolver)
    registerCredentialStorage(credentialStorage)
    registerTranslationResolver()
    registerWebBridgeActionHandler()
  }

  private func registerAppEnvironment(
    isDebugOrBetaBuild: Bool = false,
    isDebugBuild: Bool = false
  ) -> OGAppEnvironment {
    let appEnvironment = OGAppEnvironment(
      isDebugOrBetaBuild: isDebugOrBetaBuild,
      isDebugBuild: isDebugBuild
    )
    OGCoreContainer.shared.appEnvironment.register {
      appEnvironment
    }
    return appEnvironment
  }

  private func registerFirebase() {
    _ = OGFirebaseContainer.shared.firebase()
  }

  private func registerTranslationResolver() {
    _ = L10nContainer.shared.translationResolver()
  }

  private func registerStores() {
    _ = AppContainer.shared.appStore()
    _ = AppContainer.shared.screenAnnouncerStore()
    _ = PromoBannerContainer.shared.promoListener()
    _ = OGAppLifecycleContainer.shared.appLifecycleStore()
    _ = OGDialogCoordinatorContainer.shared.dialogCoordinatorStore()
    _ = OGTrackerOptInContainer.shared.trackingOptInStore()
    _ = SalutationContainer.shared.store()
    _ = AppTrackerStore.make()
    _ = NativeAPIContainer.shared.store()
  }

  private func registerWebBridgeActionHandler() {
    let globalGetWebBridge = OGWebBridgeContainer.shared.globalGetWebBridge()
    globalGetWebBridge.addActionHandler(ProductDetailEnabledWebGetBridgeActionHandler())
  }

  private func registerTrackingReporters() {
    let tracker = OGTrackerContainer.shared.tracker()
    Task {
      _ = await tracker.register(
        [
          OGAdjustReporterContainer.shared.reporter(),
          OGFirebaseReporterContainer.shared.reporter()
        ]
      )
      _ = OGWebBridgeTrackerContainer.shared.webBridgeTracker()
    }
  }

  private func registerRouteProvider() {
    OGRoutingContainer.shared.routeProvider.register {
      OGRouteProvider(webViewIdentifier: .webView)
    }
  }

  private func registerDestinationProviders(
    appEnvironment: OGAppEnvironment
  ) {
    OGRoutingContainer.shared.router.register {
      var destinationProviders: [(any OGDestinationProvidable)?] = [
        AccountDestinationProvider(),
        AccountDetailDestinationProvider(),
        AssortmentDestinationProvider(),
        AssortmentDetailDestinationProvider(),
        DealsDestinationProvider(),
        DetailStoreDestinationProvider(),
        InboxDestinationProvider(),
        InboxDetailDestinationProvider(),
        LoginDestinationProvider(),
        LocationPermissionAlertDestinationProvider(),
        ModalTenantChooserDestinationProvider(),
        OGExternalBrowserDestinationProvider(),
        OGInAppBrowserDestinationProvider(),
        OGSharingDestinationProvider(),
        OGWebViewDestinationProvider(),
        PromotionValidBannerDestinationProvider(),
        PromotionInvalidBannerDestinationProvider(),
        PushDialogDestinationProvider(),
        PushPromotionProvider(),
        PushTenantChooserDestinationProvider(),
        RegisterDestinationProvider(),
        ReviewDestinationProvider(),
        SearchDestinationProvider(),
        StoreFinderDestinationProvider(),
        SystemSettingDestinationProvider(),
        TenantSwitchDestinationProvider(),
        WelcomeViewDestinationProvider(),
        AccountSettingsDestinationProvider(),
        RecommendationDestinationProvider(),
        ProductDetailDestinationProvider(),
        ProductReviewDetailDestinationProvider(),
        GalleryViewDestinationProvider(),
        AddToBasketSuccessViewDestinationProvider(),
        VariantSelectionDestinationProvider(),
        ErrorBannerViewDestinationProvider(),
        ProductDetailSubFlowDestinationProvider(),
        MotionSettingSheetDestinationProvider(),
        SizeAdvisorViewDestinationProvider()
      ]

      destinationProviders.append(contentsOf: BfgDestinationProviders.all)
      destinationProviders.append(contentsOf: CatalogScannerDestinationProviders.all)

      if appEnvironment.isDebugOrBetaBuild {
        destinationProviders.append(DetectiveViewDestinationProvider())
      }

      let providers = destinationProviders.compactMap { $0 }

      let publisher = OGRoutingContainer.shared.routePublisher()
      publisher.setDestinationProviders(providers)

      return OGRouter(providers: providers)
    }
  }

  private func registerSecretService(
    secrets: [OGSecret]
  ) {
    OGSecretServiceContainer.shared.service.register {
      OGSecretService(secrets: Set(secrets))
    }
  }

  private func registerTenantService(
    tenantService: OGTenantServiceable?
  ) {
    guard let tenantService else { return }
    OGTenantContainer.shared.service.register {
      tenantService
    }
  }

  func registerLoggers(isDebugBuild: Bool) {
    guard isDebugBuild else { return }
    let consoleLogger = OGCoreContainer.shared.consoleLogger()
    receivables.append(contentsOf: [consoleLogger])
  }

  private func registerFeatureManager() {
    OGFeatureManagerContainer.shared.featureManger.register {
      OGFeatureManager(
        defaultProvider: OGBundledFeatureSetFetcherContainer.shared.bundledFeatureSetFetcher(),
        remoteProvider: OGRemoteFeatureSetFetcherContainer.shared.firebaseRemoteConfig(),
        testEnvironmentProvider: OGTestEnvironmentsContainer.shared
          .testEnvironmentFeatureSetPublisher(),
        baseProvider: OGBundledFeatureSetFetcherContainer.shared.bundledBaseFeatureSetFetcher()
      )
    }
  }

  private func registerDetective(isDebugOrBetaBuild: Bool) {
    guard isDebugOrBetaBuild else { return }
    let detective = OGDetectiveContainer.shared.detective()
    detective.setModuleOrder(
      [
        .logs: [
          OGDetective.DefaultModules.log,
          OGDetective.DefaultModules.network,
          OGDetective.DefaultModules.event
        ],
        .appInformation: [
          OGDetective.DefaultModules.appInformation,
          OGTenantEnvironmentModule(),
          OGFeatureModule()
        ],
        .snapshot: [
          OGDetective.DefaultModules.snapshot
        ]
      ]
    )
  }

  private func registerFontResolver(_ fontResolver: OGFontResolvable) {
    FontResolverContainer.shared.fontResolver.register {
      fontResolver
    }
  }

  private func registerCredentialStorage(_ credentialStorage: OGURLCredentialStoring?) {
    if let credentialStorage {
      OGURLCredentialStorageContainer.shared.credentialStorage.register {
        credentialStorage
      }
    } else {
      _ = OGURLCredentialStorageContainer.shared.credentialStorage()
    }
  }

  private func setupTheme() {
    AppearanceCoordinator.customizeAppearance()
  }
}
