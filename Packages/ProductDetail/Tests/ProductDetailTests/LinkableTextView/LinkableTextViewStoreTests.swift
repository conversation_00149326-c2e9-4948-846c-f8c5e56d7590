import OGRouter
import OGRouterTestsUtils
import SwiftUI
import UICatalog
import XCTest
@testable import ProductDetail

// MARK: - LinkableTextViewStoreTests

final class LinkableTextViewStoreTests: XCTestCase {
  func test_GIVEN_viewState_WHEN_linkTapped_THEN_routerSendsRoute() async throws {
    // Given
    let url = URL(string: "https://example.com")!
    let routerMock = OGRoutePublisherMock()
    let sut = LinkableTextView.Middleware(router: routerMock)
    let state = LinkableTextView.ViewState(
      fullText: "Test text",
      linkText: "link",
      url: url,
      linkColor: .red,
      textColor: .black
    )

    // When
    let result = await sut.callAsFunction(
      event: .linkTapped,
      for: state
    )

    // Then
    XCTAssertNil(result)
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 1)
    let sentRoute = routerMock.mock.sendCalls.latestCall
    XCTAssertEqual(sentRoute?.url, url)
  }

  func test_GIVEN_viewState_WHEN_linkTappedEvent_THEN_stateUnchanged() {
    // Given
    var state = LinkableTextView.ViewState(
      fullText: "Test text",
      linkText: "link",
      url: URL(string: "https://example.com")!,
      linkColor: .red,
      textColor: .black
    )
    let originalState = state

    // When
    LinkableTextView.Reducer.reduce(&state, with: .linkTapped)

    // Then - State should remain unchanged as reducer doesn't modify state for this event
    XCTAssertEqual(state.fullText, originalState.fullText)
    XCTAssertEqual(state.linkText, originalState.linkText)
    XCTAssertEqual(state.url, originalState.url)
  }

  func test_GIVEN_viewState_WHEN_attributedText_THEN_correctAttributedStringCreated() {
    // Given
    let state = LinkableTextView.ViewState(
      fullText: "Click {here} for more info",
      linkText: "here",
      url: URL(string: "https://example.com")!,
      linkColor: .blue,
      textColor: .black
    )

    // When
    let attributedText = state.attributedText

    // Then
    XCTAssertTrue(!attributedText.characters.isEmpty)
    // The attributed text should contain the processed text without the curly braces
    XCTAssertTrue(String(attributedText.characters).contains("Click here for more info"))
  }

  func test_GIVEN_viewStateWithoutLinkText_WHEN_attributedText_THEN_fullTextUsedAsLink() {
    // Given
    let state = LinkableTextView.ViewState(
      fullText: "Click here",
      linkText: nil,
      url: URL(string: "https://example.com")!,
      linkColor: .blue,
      textColor: .black
    )

    // When
    let attributedText = state.attributedText

    // Then
    XCTAssertTrue(!attributedText.characters.isEmpty)
    XCTAssertEqual(String(attributedText.characters), "Click here")
  }
}

// MARK: - LinkableTextView.Event + Equatable

extension LinkableTextView.Event: Equatable {
  static func == (lhs: LinkableTextView.Event, rhs: LinkableTextView.Event) -> Bool {
    switch (lhs, rhs) {
    case (.linkTapped, .linkTapped):
      return true
    }
  }
}
