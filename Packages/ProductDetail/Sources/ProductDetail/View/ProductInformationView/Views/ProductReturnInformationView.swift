import OGAppKitSDK
import OGDIService
import OGL10n
import SwiftUI
import UICatalog

struct ProductReturnInformationView: SwiftUI.View {
  let url: URL
  let isLoading: Bool

  var body: some SwiftUI.View {
    DropdownView(isLoading: isLoading, name: ogL10n.ProductDetail.Information.Return.Title) {
      Text(ogL10n.ProductDetail.Information.Return.Title)
        .font(for: .copyL)
        .foregroundColor(OGColors.textOnLight.color)
    } contentView: {
      VStack(alignment: .leading) {
        LinkableTextView(
          ogL10n.ProductDetail.Information.Return.Copy,
          linkText: ogL10n.ProductDetail.Information.Return.Link,
          url: url
        )
        .multilineTextAlignment(.leading)
      }
      .frame(maxWidth: .infinity, alignment: .leading)
      .padding(.vertical, UILayoutConstants.Default.padding)
      .padding(.trailing, UILayoutConstants.Default.padding2x)
    }
  }
}
