import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

// MARK: - ProductArticleStandardsView

struct ProductArticleStandardsView: SwiftUI.View {
  let articleStandards: ArticleStandards
  let isLoading: Bool
  var body: some SwiftUI.View {
    DropdownView(isLoading: isLoading, name: ogL10n.ProductDetail.Information.ArticleStandards.Title) {
      HStack {
        Text(ogL10n.ProductDetail.Information.ArticleStandards.Title)
          .font(for: .copyL)
          .foregroundColor(OGColors.textOnLight.color)
        categoryIcons
      }
    } contentView: {
      VStack(alignment: .leading) {
        seals
        sealsUrls
      }
      .padding(.vertical, UILayoutConstants.Default.padding)
      .padding(.trailing, UILayoutConstants.Default.padding2x)
      .frame(maxWidth: .infinity, alignment: .leading)
    }
  }

  var seals: some SwiftUI.View {
    ForEach(Array(articleStandards.seals.enumerated()), id: \.offset) { index, seal in
      row(icon: seal.category.imageNameLarge, title: seal.category.title, detail: seal.category.detail)
      ComponentDivider()
        .padding(.vertical, UILayoutConstants.Default.padding2x)
      row(icon: seal.imageName, title: seal.title, detail: seal.detail)
      if index < articleStandards.seals.count - 1 {
        ComponentDivider()
          .padding(.vertical, UILayoutConstants.Default.padding2x)
      }
    }
  }

  @ViewBuilder var sealsUrls: some SwiftUI.View {
    if !articleStandards.sealsUrls.isEmpty {
      Text(ogL10n.ProductDetail.Information.Sustainability.Copy)
        .font(for: .copyMRegular)
        .foregroundColor(OGColors.textOnLight.color)
    }
    ForEach(Array(articleStandards.sealsUrls.enumerated()), id: \.offset) { index, url in
      AsyncCachedImage(url: url) { image in
        image
          .resizable()
          .aspectRatio(contentMode: .fit)
          .frame(width: UILayoutConstants.ProductArticleStandardsView.imageWidth)
          .accessibilityHidden(true)
      } placeholder: { loadingState in
        if case .failure = loadingState {
          OGImages.icon24x24PlaceholderImg.image
        } else {
          Rectangle()
            .fill(.clear)
            .frame(width: UILayoutConstants.ProductArticleStandardsView.imageWidth)
            .frame(height: UILayoutConstants.ProductArticleStandardsView.imageWidth)
            .shimmering()
        }
      }
      if index < articleStandards.sealsUrls.count - 1 {
        ComponentDivider()
          .padding(.vertical, UILayoutConstants.Default.padding2x)
      }
    }
    if let informationLink = articleStandards.informationLink, let url = URL(string: informationLink) {
      LinkableTextView(
        ogL10n.ProductDetail.Information.Sustainability.MoreInfo,
        url: url
      )
      .multilineTextAlignment(.leading)
    }
  }

  func row(icon: String?, title: String, detail: String) -> some SwiftUI.View {
    HStack(alignment: .top) {
      VStack(alignment: .leading) {
        if let icon {
          Image(icon)
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(width: UILayoutConstants.ProductArticleStandardsView.imageWidth)
            .accessibilityHidden(true)
        }
      }.frame(width: UILayoutConstants.ProductArticleStandardsView.sectionWidth)

      VStack(alignment: .leading) {
        Text(title)
          .foregroundColor(OGColors.textOnLight.color)
          .font(for: .titleM)
          .padding(.bottom, UILayoutConstants.Default.padding)
        Text(detail)
          .foregroundColor(OGColors.textOnLight.color)
          .font(for: .copyMRegular)
          .multilineTextAlignment(.leading)
      }
    }
    .frame(maxWidth: .infinity, alignment: .leading)
  }

  private var categoryIcons: some SwiftUI.View {
    ForEach(articleStandards.seals, id: \.self) { seal in
      Image(seal.category.imageName)
        .accessibilityLabel(seal.category.title)
    }
  }
}

// MARK: - UILayoutConstants.ProductArticleStandardsView

extension UILayoutConstants {
  enum ProductArticleStandardsView {
    static let sectionWidth: CGFloat = 60
    static let imageWidth: CGFloat = 44
  }
}
