import OGAppKitSDK
import OGCore
import OGRouter
import Swift<PERSON>

// MARK: - SizeAdvisorViewDestinationProvider

public struct SizeAdvisorViewDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier

  public init() {
    self.identifier = OGIdentifier.sizeAdvisor
  }

  public func provide(_ route: OGRoute) -> some SwiftUI.View {
    if let sizeTableData: SizeTableData = route.getData() {
      SizeAdvisorView(sizeTableData: sizeTableData)
    } else {
      EmptyView()
    }
  }

  public func presentationType() -> OGPresentationType {
    .sheet([.large])
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}

extension OGIdentifier {
  public static let sizeAdvisor = #identifier("sizeAdvisor")
}

extension OGRoute {
  public static let sizeAdvisor = OGRoute(OGIdentifier.sizeAdvisor.value)
}
