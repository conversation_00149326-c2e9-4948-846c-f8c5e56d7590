import Foundation
import OGRouter
import OGViewStore

// MARK: - SizeAdvisorView.Store

extension SizeAdvisorView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore() -> Store {
    Store(
      initialState: .initial,
      reducer: Reducer.reduce,
      middleware: Middleware()
    )
  }
}

// MARK: - SizeAdvisorView.ViewState

extension SizeAdvisorView {
  struct ViewState: OGViewState {
    static var initial: Self {
      .init()
    }
  }
}

// MARK: - SizeAdvisorView.Event

extension SizeAdvisorView {
  enum Event: OGViewEvent {
    case dismiss
  }
}

// MARK: - SizeAdvisorView.Reducer

extension SizeAdvisorView {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _ state: inout ViewState,
      with event: Event
    ) {
      switch event {
      case .dismiss:
        break
      }
    }
  }
}

// MARK: - SizeAdvisorView.Middleware

extension SizeAdvisorView {
  struct Middleware: OGViewStoreMiddleware {
    private let router: OGRoutePublishing

    init(router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher()) {
      self.router = router
    }

    func callAsFunction(
      event: Event,
      for state: ViewState
    ) async -> Event? {
      switch event {
      case .dismiss:
        router.dismiss(route: .sizeAdvisor)
        return nil
      }
    }
  }
}
