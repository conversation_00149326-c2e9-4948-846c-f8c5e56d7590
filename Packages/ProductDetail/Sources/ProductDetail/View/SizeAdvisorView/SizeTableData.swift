import OGAppKitSDK
extension OGAppKitSDK.SizeTableData {
  var toSizeTableData: SizeTableData {
    SizeTableData(
      tables: tables.map(\.toTableData),
      detailsHeadline: detailsHeadline,
      detailsDescription: detailsDescription,
      detailsItems: detailsItems.map(\.toDetailsItem),
      detailsImageUrl: detailsImageUrl,
      tipText: tipText
    )
  }
}

extension OGAppKitSDK.SizeTableDetailsItem {
  var toSizeTableDetailsItem: SizeTableDetailsItem {
    SizeTableDetailsItem(title: title, description: description_)
  }

  var toDetailsItem: DetailsItem {
    DetailsItem(title: title, description: description_)
  }
}

extension OGAppKitSDK.SizeTable {
  var toTableData: TableData {
    TableData(
      headline: headline,
      tableDetailsItems: tableDetailsItems?.map(\.toSizeTableDetailsItem),
      table: table
    )
  }
}

// MARK: - SizeTableData

struct SizeTableData: Codable, Equatable {
  let tables: [TableData]
  let detailsHeadline: String?
  let detailsDescription: String?
  let detailsItems: [DetailsItem]
  let detailsImageUrl: String?
  let tipText: String?
}

// MARK: - TableData

struct TableData: Codable, Equatable {
  let headline: String?
  let tableDetailsItems: [SizeTableDetailsItem]?
  let table: [String: [String]]
}

// MARK: - SizeTableDetailsItem

struct SizeTableDetailsItem: Codable, Equatable {
  let title: String?
  let description: String
}

// MARK: - DetailsItem

struct DetailsItem: Codable, Equatable {
  let title: String?
  let description: String
}
