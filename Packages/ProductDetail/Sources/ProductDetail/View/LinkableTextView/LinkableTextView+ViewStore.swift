import Foundation
import OGRouter
import OGViewStore
import SwiftUI
import UICatalog

// MARK: - LinkableTextView.Store

extension LinkableTextView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore(
    fullText: String,
    linkText: String?,
    url: URL,
    linkColor: Color,
    textColor: Color
  ) -> Store {
    Store(
      initialState: .init(
        fullText: fullText,
        linkText: linkText,
        url: url,
        linkColor: linkColor,
        textColor: textColor
      ),
      reducer: Reducer.reduce,
      middleware: Middleware()
    )
  }
}

// MARK: - LinkableTextView.ViewState

extension LinkableTextView {
  struct ViewState: OGViewState {
    let fullText: String
    let linkText: String?
    let url: URL
    let linkColor: Color
    let textColor: Color

    var attributedText: AttributedString {
      createAttributedText()
    }

    static var initial: Self {
      .init(
        fullText: "",
        linkText: nil,
        url: .empty,
        linkColor: OGColors.textPrimary.color,
        textColor: OGColors.textOnLight.color
      )
    }

    init(
      fullText: String,
      linkText: String?,
      url: URL,
      linkColor: Color,
      textColor: Color
    ) {
      self.fullText = fullText
      self.linkText = linkText
      self.url = url
      self.linkColor = linkColor
      self.textColor = textColor
    }

    private func createAttributedText() -> AttributedString {
      let processedText: String
      let targetText: String

      if let linkText {
        processedText = fullText.replacingOccurrences(
          of: "{\(linkText)}",
          with: linkText
        )
        targetText = linkText
      } else {
        processedText = fullText
        targetText = fullText
      }
      let attributedString = NSMutableAttributedString(string: processedText)

      attributedString.addAttribute(
        .foregroundColor,
        value: UIColor(textColor),
        range: NSRange(location: 0, length: processedText.count)
      )

      let range: NSRange = {
        if linkText != nil {
          if let range = processedText.range(of: targetText) {
            return NSRange(range, in: processedText)
          } else {
            return NSRange(location: 0, length: processedText.count)
          }
        } else {
          return NSRange(location: 0, length: processedText.count)
        }
      }()

      attributedString.addAttribute(.link, value: "internal://link", range: range)
      attributedString.addAttribute(.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: range)
      attributedString.addAttribute(.underlineColor, value: UIColor(linkColor), range: range)
      attributedString.addAttribute(.foregroundColor, value: UIColor(linkColor), range: range)

      return AttributedString(attributedString)
    }
  }
}

// MARK: - LinkableTextView.Event

extension LinkableTextView {
  enum Event: OGViewEvent {
    case linkTapped
  }
}

// MARK: - LinkableTextView.Reducer

extension LinkableTextView {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _ state: inout ViewState,
      with event: Event
    ) {
      switch event {
      case .linkTapped:
        break
      }
    }
  }
}

// MARK: - LinkableTextView.Middleware

extension LinkableTextView {
  struct Middleware: OGViewStoreMiddleware {
    private let router: OGRoutePublishing

    init(router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher()) {
      self.router = router
    }

    func callAsFunction(
      event: Event,
      for state: ViewState
    ) async -> Event? {
      switch event {
      case .linkTapped:
        let route = OGRoute(url: state.url)
        router.send(route)
        return nil
      }
    }
  }
}
