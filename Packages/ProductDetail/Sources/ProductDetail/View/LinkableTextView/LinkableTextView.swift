import OGViewStore
import SwiftUI
import UICatalog

struct LinkableTextView: View {
  let font: OGF<PERSON>s
  @StateObject private var viewStore: Self.Store

  init(
    _ fullText: String,
    linkText: String? = nil,
    url: URL,
    linkColor: Color = OGColors.textPrimary.color,
    textColor: Color = OGColors.textOnLight.color,
    font: OGFonts = .copyMRegular
  ) {
    self.font = font
    self._viewStore = StateObject(
      wrappedValue: Self.makeStore(
        fullText: fullText,
        linkText: linkText,
        url: url,
        linkColor: linkColor,
        textColor: textColor
      )
    )
  }

  var body: some View {
    Text(viewStore.state.attributedText)
      .font(for: font)
      .environment(\.openURL, OpenURLAction { _ in
        Task {
          await viewStore.dispatch(.linkTapped)
        }
        return .handled
      })
  }
}
