
import AppCore
import Combine
import Foundation
import OGAppKitSDK
import OGCore
import <PERSON><PERSON>outer
import OGTracker
import OGViewStore
import UIKit.UIPasteboard
extension ProductDetailBannerView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore(
    promoBanner: PromoBanner
  ) -> Store {
    Store(
      initialState: ViewState(
        text: promoBanner.content.text,
        promoCode: promoBanner.content.promoCode ?? "",
        showsInfo: promoBanner.content.infoText != nil,
        infoText: promoBanner.content.infoText ?? ""
      ),
      reducer: ViewState.Reducer.reduce,
      middleware: ViewState.Middleware(),
      connector: ViewState.Connector(promoBanner: promoBanner)
    )
  }

  struct ViewState: OGViewState {
    private(set) var text: String = ""
    private(set) var didCopyCode: Bool = false
    private(set) var showsInfo: Bool = false
    private(set) var promoCode: String = ""
    private(set) var infoText: String
    private var clickTrackingEvent: Interaction.ProductDetailSelectPromotion?
    private var viewTrackingEvent: View.ProductDetailPromotion?
    private var didTrackView: Bool = false
    init(
      didCopyCode: Bool = false,
      text: String = "",
      promoCode: String = "",
      showsInfo: Bool = false,
      infoText: String = ""
    ) {
      self.didCopyCode = didCopyCode
      self.text = text
      self.promoCode = promoCode
      self.showsInfo = showsInfo
      self.infoText = infoText
    }

    mutating func update(promoBanner: PromoBanner) {
      text = promoBanner.content.text
      promoCode = promoBanner.content.promoCode ?? ""
      infoText = promoBanner.content.infoText ?? ""
      showsInfo = promoBanner.content.infoText != nil
      clickTrackingEvent = promoBanner.content.trackingEvents?.click
      viewTrackingEvent = promoBanner.content.trackingEvents?.view
    }

    mutating func update(didCopyCode: Bool? = nil, didTrackView: Bool? = nil) {
      self.didCopyCode = didCopyCode ?? self.didCopyCode
      self.didTrackView = didTrackView ?? self.didTrackView
    }

    static var initial = ViewState()
  }

  enum Event: OGViewEvent, Equatable {
    static func == (lhs: ProductDetailBannerView.Event, rhs: ProductDetailBannerView.Event) -> Bool {
      switch (lhs, rhs) {
      case (._didTrackView, ._didTrackView), (._reset, ._reset), (._wait, ._wait), (.onCopyCode, .onCopyCode), (.trackView, .trackView): return true
      case let (._setPromoBanner(lhsBanner), ._setPromoBanner(rhsBanner)):
        return lhsBanner.content == rhsBanner.content
      case (._trackInteraction, ._trackInteraction):
        return true
      default:
        return false
      }
    }

    case onCopyCode
    case trackView
    /// Private
    case _setPromoBanner(PromoBanner)
    case _trackInteraction
    case _didTrackView
    case _wait
    case _reset
  }
}

extension ProductDetailBannerView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout ProductDetailBannerView.ViewState,
      with event: ProductDetailBannerView.Event
    ) {
      switch event {
      case let ._setPromoBanner(promoBanner):
        state.update(promoBanner: promoBanner)
      case .onCopyCode:
        state.update(didCopyCode: true)
      case ._didTrackView:
        state.update(didTrackView: true)
      case ._reset:
        state.update(didCopyCode: false)
      case ._trackInteraction, ._wait, .trackView: break
      }
    }
  }

  final class Middleware: OGViewStoreMiddleware {
    private var pasteboard: PasteboardServing
    private var tracker: OGTrackerProtocol
    private let resetWaitDuration: UInt64
    init(
      pasteboard: PasteboardServing = UIPasteboard.general,
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker(),
      resetWaitDuration: UInt64 = 5_000_000_000
    ) {
      self.pasteboard = pasteboard
      self.tracker = tracker
      self.resetWaitDuration = resetWaitDuration
    }

    func callAsFunction(
      event: ProductDetailBannerView.Event,
      for state: ProductDetailBannerView.ViewState
    ) async -> ProductDetailBannerView.Event? {
      switch event {
      case ._setPromoBanner:
        return nil
      case .onCopyCode:
        pasteboard.string = state.promoCode
        return ._trackInteraction
      case ._trackInteraction:
        guard let clickTrackingEvent = state.clickTrackingEvent else { return nil }
        tracker.multiplatformTrack(event: clickTrackingEvent)
        return ._wait
      case ._wait:
        try? await Task.sleep(nanoseconds: resetWaitDuration)
        return ._reset
      case .trackView:
        guard let viewTrackingEvent = state.viewTrackingEvent, !state.didTrackView else { return nil }
        tracker.multiplatformTrack(event: viewTrackingEvent)
        return ._didTrackView
      case ._didTrackView, ._reset:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    let promoBanner: PromoBanner

    init(
      promoBanner: PromoBanner
    ) {
      self.promoBanner = promoBanner
    }

    func configure(
      dispatch: @escaping (ProductDetailBannerView.Event) async -> Void
    ) async {
      await dispatch(._setPromoBanner(promoBanner))
    }
  }
}
