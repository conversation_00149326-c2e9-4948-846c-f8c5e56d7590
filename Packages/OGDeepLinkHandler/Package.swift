// swift-tools-version: 5.7
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
  name: "OGDeepLinkHandler",
  platforms: [.iOS(.v15)],
  products: [
    // Products define the executables and libraries a package produces, making them visible to other packages.
    .library(
      name: "OGDeepLinkHandler",
      targets: ["OGDeepLinkHandler"]
    )
  ],
  dependencies: [
    .package(path: "../OGCore"),
    .package(path: "../OGRouter"),
    .package(path: "../OGExternalDependencies/OGDIService"),
    .package(path: "../OGFeatureKit/Packages/OGFeatureAdapter")

  ],
  targets: [
    // Targets are the basic building blocks of a package, defining a module or a test suite.
    // Targets can depend on other targets in this package and products from dependencies.
    .target(
      name: "OGDeepLinkHandler",
      dependencies: [
        "OGCore",
        "OGRouter",
        "OGDIService",
        "OGFeatureAdapter"
      ]
    ),
    .testTarget(
      name: "OGDeepLinkHandlerTests",
      dependencies: [
        "OGDeepLinkHandler",
        .product(name: "OGRouterTestsUtils", package: "OGRouter"),
        .product(name: "OGFeatureAdapterTestsUtils", package: "OGFeatureAdapter")
      ]
    )
  ]
)
