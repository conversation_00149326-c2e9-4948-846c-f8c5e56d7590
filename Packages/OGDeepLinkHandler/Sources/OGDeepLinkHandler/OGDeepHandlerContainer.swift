import Foundation
import OGDIService

/// The Dependency Injection Container
public final class OGDeepHandlerContainer: OGDISharedContainer {
  public static let shared: OGDeepHandlerContainer = .init()

  public var manager: OGDIContainerManager = .init()

  /// The Instance of the OGDeepLinking
  public var deepLinkHandler: OGDIService<OGDeepLinkReceivable> {
    self {
      OGDeepLinkHandler()
    }.cached
  }

  public var deepLinkCleaner: OGDIService<OGDeepLinkCleaning> {
    self {
      OGDeepLinkCleaner()
    }.cached
  }
}
