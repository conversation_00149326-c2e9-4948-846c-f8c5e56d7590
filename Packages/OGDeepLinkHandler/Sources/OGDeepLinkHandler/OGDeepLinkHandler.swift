import Foundation
import OGCore
import OGDIService
import OGFeatureAdapter
import <PERSON><PERSON><PERSON><PERSON>
import SwiftUI

// MARK: - OGDeepLinkReceivable

/// Protocol for handling deep links URLs.
public protocol OGDeepLinkReceivable {
  /// Handles an incoming URL.
  /// - Parameter url: The URL to be handled.
  func handleIncomingURL(_ url: URL)
}

// MARK: - OGDeepLinkHandler

/// Class responsible for handling deep links URLs.
public struct OGDeepLinkHandler: OGDeepLinkReceivable {
  @OGInjected(\OGRoutingContainer.routePublisher) private var router
  @OGInjected(\OGCoreContainer.logger) private var logger
  @OGInjected(\OGBaseUrlFeatureAdapterContainer.baseUrl) private var baseUrl
  @OGInjected(\OGDeepHandlerContainer.deepLinkCleaner) private var deepLinkCleaner
  @OGInjected(\OGCoreContainer.deviceDeepLinkReceiver) private var deviceDeepLinkReceiver

  private let bundle: Bundle

  /// Initializes the deep linking service with a given bundle.
  /// - Parameter bundle: The bundle to use for getting app schemes. Defaults to `Bundle.main`.
  public init(bundle: Bundle = Bundle.main) {
    self.bundle = bundle
  }

  /// Handles an incoming URLs and sends it to the router as an `OGRoute` object.
  /// - Parameter url: The URL to be handled.
  public func handleIncomingURL(_ url: URL) {
    logger.debug(domain: .service, message: "App was opened via URL: \(url)")
    deviceDeepLinkReceiver.set(deepLink: url)
    if let scheme = url.scheme, bundle.appSchemes().map({ $0.lowercased() }).contains(where: { $0 == scheme.lowercased() }) {
      var absoluteString = url.absoluteString.dropFirst("\(scheme)://".count)
      let regex = "\\b\\w+\\/\\/(?!\\/)[^\\s]+"
      if absoluteString.range(of: regex, options: .regularExpression, range: nil, locale: nil) != nil,
         let index = absoluteString.firstIndex(of: Character("/")) {
        absoluteString.insert(Character(":"), at: index)
        router.send(createRoute(for: String(absoluteString)))
      } else {
        router.send(createRoute(for: String(absoluteString)))
      }
    } else {
      router.send(createRoute(for: url.absoluteString))
    }
  }

  private func createRoute(for string: String) -> OGRoute {
    var url = baseUrl.urlRelativeToWebUrl(forUrlPath: string)?
      .addSchemeIfNeeded()
    deepLinkCleaner.urls.forEach {
      url = url?.remove(url: $0)
    }

    return OGRoute(url: url)
  }
}

extension URL {
  func addSchemeIfNeeded() -> URL {
    if scheme == nil {
      URL(string: "https://\(absoluteString)") ?? self
    } else {
      self
    }
  }

  func remove(url: String?) -> URL {
    guard let url,
          absoluteString.contains(url),
          let cleanString = absoluteString.replacingOccurrences(of: url, with: "").removingPercentEncoding,
          let cleanURL = URL(string: cleanString) else { return self }
    return cleanURL
  }
}
