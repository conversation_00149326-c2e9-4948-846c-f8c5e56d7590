import OGCore
import OGDIService
import SwiftUI
extension View {
  /// Adds an `onOpenURL` handler to the content view and sends to a deep link handler
  public func onDeepLink() -> some View {
    modifier(DeepLink())
  }
}

// MARK: - DeepLink

struct DeepLink: ViewModifier {
  @OGInjected(\OGDeepHandlerContainer.deepLinkHandler) private var deepLinkHandler
  @OGInjected(\OGCoreContainer.logger) private var logger
  private let delay: UInt64 = 500_000_000 // 0.5 seconds
  func body(content: Content) -> some View {
    content
      .onContinueUserActivity(NSUserActivityTypeBrowsingWeb, perform: { activity in
        guard let url = activity.webpageURL else {
          logger.log(.critical, domain: .service, message: "missing UserActivity")
          return
        }
        Task { @MainActor in
          try? await Task.sleep(nanoseconds: delay)
          deepLinkHandler.handleIncomingURL(url)
        }
      })
      .onOpenURL { url in
        Task { @MainActor in
          try? await Task.sleep(nanoseconds: delay)
          deepLinkHandler.handleIncomingURL(url)
        }
      }
  }
}
