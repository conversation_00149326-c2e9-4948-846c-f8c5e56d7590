import Foundation

// MARK: - OGDeepLinkCleaning

/// Protocol for holding cleaning URLs.
public protocol OGDeepLinkCleaning {
  /// Adds cleaning URLs.
  /// - Parameter url: The URL to be removed from the deep link .
  func urlToClean(_ url: String)

  /// current urls
  var urls: Set<String> { get }
}

// MARK: - OGDeepLinkCleaner

/// Class responsible for holding cleaning URLs.
public final class OGDeepLinkCleaner: OGDeepLinkCleaning {
  public private(set) var urls: Set<String> = Set([String]())
  public init() {}

  public func urlToClean(_ url: String) {
    urls.insert(url)
  }
}
