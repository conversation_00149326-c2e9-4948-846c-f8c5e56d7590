import OGAppKitSDK
import OGCoreTestsUtils
import XCTest

@testable import NativeAPI

final class NativeAPIStoreMiddlewareTests: XCTestCase {
  /// Test: GIVEN a valid API tenant and configuration details, WHEN _receiveConfig is dispatched, THEN nativeAPI.configure is called and _isConfigured(true) is returned
  func test_GIVEN_validApiTenant_WHEN_receiveConfig_THEN_configureCalledAndIsConfiguredTrue() async throws {
    // GIVEN
    let action = NativeAPIAction._receiveConfig(
      graphQLApiUrl: .stub,
      productIdRegex: .stub,
      wittConfig: WittConfig(locale: .stub, displayPaybackPoints: false, cookies: WittConfig.Cookies(url: .stub, tokenCookieName: .stub, recoSessionIdCookieName: .stub)),
      lascanaConfig: nil,
      user: .stub,
      password: .stub,
      dynamicYieldApiKey: nil
    )

    let mockNativeAPI = MockNativeAPI()
    let mockCookiesBridge = MockCookiesBridge()
    let sut = NativeAPIState.Middleware(nativeAPI: mockNativeAPI, nativeAPICookiesBridge: mockCookiesBridge)

    // WHEN
    let resultAction = try await sut(action: action, for: .initial)
    let nativeConfig = OGNativeConfigWitt(
      graphQLBackend: OGNativeConfigBackend(
        url: .stub,
        basicAuthUser: .stub,
        basicAuthPassword: .stub
      ),
      productIdRegex: .stub,
      cookiesBridge: mockCookiesBridge,
      locale: .stub,
      cookies: OGNativeConfigWitt.Cookies(
        url: .stub,
        tokenCookieName: .stub,
        recoSessionIdCookieName: .stub
      ),
      webShopBaseUrl: .stub,
      displayPaybackPoints: false,
      debug: false
    )
    // THEN
    XCTAssertEqual(mockNativeAPI.mock.configureCalls.callsCount, 1)
    XCTAssertEqual(mockNativeAPI.mock.configureCalls.latestCall?.productIdRegex, nativeConfig.productIdRegex)
    XCTAssertEqual(resultAction, ._isConfigured(true))
  }

  /// Test: GIVEN an invalid API tenant, WHEN _receiveConfig is dispatched, THEN nativeAPI.configure is not called and _isConfigured(false) is returned
  func test_GIVEN_invalidApiTenant_WHEN_receiveConfig_THEN_configureNotCalledAndIsConfiguredFalse() async throws {
    // GIVEN
    let action = NativeAPIAction._receiveConfig(
      graphQLApiUrl: .stub,
      productIdRegex: .stub,
      wittConfig: nil,
      lascanaConfig: nil,
      user: .stub,
      password: .stub,
      dynamicYieldApiKey: nil
    )

    let mockNativeAPI = MockNativeAPI()
    let mockCookiesBridge = MockCookiesBridge()
    let sut = NativeAPIState.Middleware(nativeAPI: mockNativeAPI, nativeAPICookiesBridge: mockCookiesBridge)

    // WHEN
    let resultAction = try await sut(action: action, for: .initial)

    // THEN
    XCTAssertEqual(mockNativeAPI.mock.configureCalls.callsCount, 0)
    XCTAssertEqual(resultAction, ._isConfigured(false))
  }

  /// Test: GIVEN an action of _isConfigured, WHEN the middleware processes it, THEN no further action is returned
  func test_GIVEN_isConfiguredAction_WHEN_processed_THEN_noFurtherActionReturned() async throws {
    // GIVEN
    let action = NativeAPIAction._isConfigured(true)

    let mockNativeAPI = MockNativeAPI()
    let mockCookiesBridge = MockCookiesBridge()
    let sut = NativeAPIState.Middleware(nativeAPI: mockNativeAPI, nativeAPICookiesBridge: mockCookiesBridge)

    // WHEN
    let resultAction = try await sut(action: action, for: .initial)

    // THEN
    XCTAssertNil(resultAction)
  }
}
