import XCTest

@testable import OGAppLifecycle

final class OGAppLifecycleStateReducerTests: XCTestCase {
  func test_WHEN_didBecomeActive_THEN_appLifeCycleState_equals_active() throws {
    var state = OGAppLifecycleState()
    OGAppLifecycleState.Reducer.reduce(&state, with: ._didBecomeActive)

    let expected = OGAppLifecycleState(appLifeCycleState: .active)

    XCTAssertEqual(state, expected)
  }

  func test_WHEN_didEnterBackground_THEN_appLifeCycleState_equals_background() throws {
    var state = OGAppLifecycleState()
    OGAppLifecycleState.Reducer.reduce(&state, with: ._didEnterBackground)

    let expected = OGAppLifecycleState(appLifeCycleState: .background)

    XCTAssertEqual(state, expected)
  }

  func test_WHEN_didFinishLaunch_THEN_appLifeCycleState_equals_started() throws {
    var state = OGAppLifecycleState()
    OGAppLifecycleState.Reducer.reduce(&state, with: ._didFinishLaunch)

    let expected = OGAppLifecycleState(appLifeCycleState: .started)

    XCTAssertEqual(state, expected)
  }

  func test_WHEN_willEnterForeground_THEN_appLifeCycleState_equals_foreground() throws {
    var state = OGAppLifecycleState()
    OGAppLifecycleState.Reducer.reduce(&state, with: ._willEnterForeground)

    let expected = OGAppLifecycleState(appLifeCycleState: .foreground)

    XCTAssertEqual(state, expected)
  }
}
