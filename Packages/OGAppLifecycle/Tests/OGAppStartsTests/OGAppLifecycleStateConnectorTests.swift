import OGCoreTestsUtils
import XCTest

@testable import OGAppLifecycle

final class OGAppLifecycleStateConnectorTests: XCTestCase {
  func test_WHEN_didBecomeActiveNotification_THEN_dispatch_didBecomeActive() async throws {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Expected set event to be received")

      let sut = OGAppLifecycleState.Connector()
      var actualEvents: [OGAppLifecycleAction] = []

      let dispatch = { event in
        actualEvents.append(event)
        if actualEvents.count == 1 {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)
      NotificationCenter.default.post(name: UIApplication.didBecomeActiveNotification, object: nil)
      await fulfillment(of: [expectation], timeout: 0.1)

      let expectedEvents: [OGAppLifecycleAction] = [._didBecomeActive]
      XCTAssertEqual(actualEvents, expectedEvents)
    }
  }

  func test_WHEN_didEnterBackgroundNotification_THEN_dispatch_didEnterBackground() async throws {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Expected set event to be received")

      let sut = OGAppLifecycleState.Connector()
      var actualEvents: [OGAppLifecycleAction] = []

      let dispatch = { event in
        actualEvents.append(event)
        if actualEvents.count == 1 {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)
      NotificationCenter.default.post(name: UIApplication.didEnterBackgroundNotification, object: nil)
      await fulfillment(of: [expectation], timeout: 0.1)

      let expectedEvents: [OGAppLifecycleAction] = [._didEnterBackground]
      XCTAssertEqual(actualEvents, expectedEvents)
    }
  }

  func test_WHEN_didFinishLaunchingPublisher_THEN_dispatch_didFinishLaunch() async throws {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Expected set event to be received")

      let sut = OGAppLifecycleState.Connector()
      var actualEvents: [OGAppLifecycleAction] = []

      let dispatch = { event in
        actualEvents.append(event)
        if actualEvents.count == 1 {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)
      NotificationCenter.default.post(name: UIApplication.didFinishLaunchingNotification, object: nil)
      await fulfillment(of: [expectation], timeout: 0.1)

      let expectedEvents: [OGAppLifecycleAction] = [._didFinishLaunch]
      XCTAssertEqual(actualEvents, expectedEvents)
    }
  }

  func test_WHEN_willEnterForegroundPublisher_THEN_dispatch_willEnterForeground() async throws {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Expected set event to be received")

      let sut = OGAppLifecycleState.Connector()
      var actualEvents: [OGAppLifecycleAction] = []

      let dispatch = { event in
        actualEvents.append(event)
        if actualEvents.count == 1 {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)
      NotificationCenter.default.post(name: UIApplication.willEnterForegroundNotification, object: nil)
      await fulfillment(of: [expectation], timeout: 0.1)

      let expectedEvents: [OGAppLifecycleAction] = [._willEnterForeground]
      XCTAssertEqual(actualEvents, expectedEvents)
    }
  }
}
