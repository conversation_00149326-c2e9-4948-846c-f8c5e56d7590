// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "OGAppLifecycle",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGAppLifecycle",
      targets: ["OGAppLifecycle"]
    )
  ],
  dependencies: [
    .package(path: "../OGCore"),
    .package(path: "../OGDomainStore"),
    .package(path: "../OGExternalDependencies/OGDIService")
  ],
  targets: [
    .target(
      name: "OGAppLifecycle",
      dependencies: [
        "OGCore",
        "OGDomainStore",
        "OGDIService"
      ]
    ),
    .testTarget(
      name: "OGAppLifecycleTests",
      dependencies: [
        "OGAppLifecycle",
        .product(name: "OGCoreTestsUtils", package: "OGCore")
      ]
    )
  ]
)
