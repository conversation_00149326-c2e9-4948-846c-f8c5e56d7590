import Foundation

// MARK: - UserDefaults + Defaultable

/// Make `UserDefaults` conform `Defaultable`.
extension UserDefaults: Defaultable {}

// MARK: - Defaultable

/// `Defaultable` is a grouped protocol of `BoolDefaultable`, `IntDefaultable` and `DataDefaultable`.
public protocol Defaultable: BoolDefaultable, IntDefaultable, DataDefaultable {}

// MARK: - BoolDefaultable

/// Used to set and get a Bool value or a defined default.
public protocol BoolDefaultable {
  func bool(forKey defaultName: String) -> Bool
  func set(_ value: Bool, forKey defaultName: String)
}

// MARK: - IntDefaultable

/// Used to set and get a Int value or a defined default.
public protocol IntDefaultable {
  func integer(forKey defaultName: String) -> Int
  func set(_ value: Int, forKey defaultName: String)
}

// MARK: - DataDefaultable

/// Used to set and get a Data value or a defined default.
public protocol DataDefaultable {
  func data(forKey defaultName: String) -> Data?
  func set(_ value: Any?, forKey defaultName: String)
}
