import Foundation

extension NSRegularExpression {
  public static func from(_ urlPattern: String) -> NSRegularExpression? {
    var expression = urlPattern
    var regexOptions: NSRegularExpression.Options = [.allowCommentsAndWhitespace, .caseInsensitive, .allowCommentsAndWhitespace]

    if let regexMarkerRange = urlPattern.range(of: "regex:") {
      expression = String(urlPattern[regexMarkerRange.upperBound...])
    } else {
      regexOptions.insert(.ignoreMetacharacters)
    }

    return try? NSRegularExpression(pattern: expression, options: regexOptions)
  }
}

extension Array where Element: NSRegularExpression {
  public init(urlPatterns: [String]) {
    // swiftlint:disable:next force_cast
    self = urlPatterns.compactMap { NSRegularExpression.from($0) } as! [Element]
  }
}
