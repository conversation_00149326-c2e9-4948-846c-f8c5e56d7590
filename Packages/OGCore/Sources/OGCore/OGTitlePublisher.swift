import Combine
import Foundation

// MARK: - OGTitlePublishing

public protocol OGTitlePublishing {
  @MainActor var provider: CurrentValueSubject<String?, Never> { get }

  @MainActor
  func send(_ title: String)
}

// MARK: - OGTitlePublisher

public final actor OGTitlePublisher: OGTitlePublishing {
  @MainActor public private(set) var provider: CurrentValueSubject<String?, Never> = CurrentValueSubject(nil)

  @MainActor
  public func send(_ title: String) {
    provider.send(title)
  }
}
