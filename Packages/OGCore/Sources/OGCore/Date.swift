import Foundation
import OGAppKitSDK

// MARK: - DateType

public enum DateType {
  case today
  case yesterday
  case inLastWeek
  case olderThanOneWeek
  case inLastYear
}

extension Date {
  public func formatter(for locale: Locale?) -> DateFormatter {
    let dateFormatter = DateFormatter()
    guard
      let locale
    else {
      dateFormatter.locale = Locale.autoupdatingCurrent
      return dateFormatter
    }
    dateFormatter.locale = locale
    return dateFormatter
  }
}

extension Date {
  private var dateType: DateType {
    getDateType()
  }

  public func getDateType(_ currentDate: Date = Date()) -> DateType {
    let calendar = Calendar.current

    if calendar.component(.day, from: self) == calendar.component(.day, from: currentDate),
       calendar.component(.month, from: self) == calendar.component(.month, from: currentDate),
       calendar.component(.year, from: self) == calendar.component(.year, from: currentDate) {
      return .today
    }

    if let previousDate = calendar.date(byAdding: .day, value: -1, to: currentDate),
       calendar.component(.day, from: self) == calendar.component(.day, from: previousDate),
       calendar.component(.month, from: self) == calendar.component(.month, from: previousDate),
       calendar.component(.year, from: self) == calendar.component(.year, from: previousDate) {
      return .yesterday
    }

    let weekInSeconds = TimeInterval(calendar.weekdaySymbols.count * 60 * 60 * 24)
    let todayStart = calendar.startOfDay(for: currentDate)
    if abs(timeIntervalSince(todayStart)) < weekInSeconds {
      return .inLastWeek
    } else if calendar.component(.year, from: self) != calendar.component(.year, from: currentDate) {
      return .inLastYear
    } else {
      return .olderThanOneWeek
    }
  }

  public func formatted(
    dateFormatter: DateFormatter,
    yesterdayLocalizable: String
  )
    -> String {
    let calendar = Calendar.current
    switch dateType {
    case .today:
      dateFormatter.dateStyle = .none
      dateFormatter.timeStyle = .short
      return dateFormatter.string(from: self)
    case .yesterday:
      return yesterdayLocalizable
    case .inLastWeek:
      let index = calendar.component(.weekday, from: self) - 1
      return dateFormatter.weekdaySymbols[index]
    case .olderThanOneWeek:
      dateFormatter.setLocalizedDateFormatFromTemplate("MMMMd")
      return dateFormatter.string(from: self)
    case .inLastYear:
      dateFormatter.setLocalizedDateFormatFromTemplate("dMyy")
      return dateFormatter.string(from: self)
    }
  }
}

extension Date {
  public init?(kotlinxDate: OGAppKitSDK.Kotlinx_datetimeInstant) {
    let interval = TimeInterval(kotlinxDate.toEpochMilliseconds())
    self = Date(timeIntervalSince1970: interval / 1_000)
  }
}
