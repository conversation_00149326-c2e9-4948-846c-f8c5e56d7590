import Foundation

// MARK: - OGLocaleProvidable

/// A protocol for providing a locale
public protocol OGLocaleProvidable {
  /// The locale provided by the conforming object
  var locale: Locale { get }
}

// MARK: - OGLocaleProvider

/// A class for providing the current locale
public class OGLocaleProvider: OGLocaleProvidable {
  /// The current locale
  public var locale: Locale {
    Locale.current
  }

  public init() {}
}
