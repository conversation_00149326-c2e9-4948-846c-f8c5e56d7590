import Foundation

// MARK: - BundledResourceError

/// Enum for bundled resource related errors
public enum BundledResourceError: Error {
  /// The resource was not found.
  case resourceNotFound(String)
}

// MARK: - BundledJsonResourceFetchable

/// A protocol that defines a type that can fetch a JSON-formatted resource from a bundle.
public protocol BundledJsonResourceFetchable: AnyObject {
  /// The type of the object that will be fetched.
  associatedtype T

  /// Fetches a JSON-formatted resource from a bundle and decodes it into an object of type `T`.
  /// - Parameters:
  ///   - resourceName: The name of the resource to fetch.
  ///   - bundle: The bundle to fetch the resource from. If `nil`, the main bundle is used.
  /// - Returns: An object of type `T` decoded from the JSON resource.
  /// - Throws: `BundledResourceError.resourceNotFound` if the resource cannot be found, or a decoding error if the resource cannot be decoded into an object of type `T`.
  static func fetch<T: Decodable>(forResource resourceName: String, inBundle bundle: Bundle?) throws -> T
}

extension BundledJsonResourceFetchable {
  /// Fetches a json file for given parameter and returns type `T`. `T` needs to conform `Decodable`.
  /// - Parameters:
  ///   - resourceName: The name of the resource to fetch.
  ///   - bundle: The bundle to fetch the resource from. If `nil`, the main bundle is used.
  ///  - Returns: An object of type `T` decoded from the JSON resource.
  /// - Throws: `BundledResourceError.resourceNotFound` if the resource cannot be found, or a decoding error if the resource cannot be decoded into an object of type `T`.
  public static func fetch<T: Decodable>(forResource resourceName: String, inBundle bundle: Bundle? = Bundle.main) throws -> T {
    let resourceExtension = "json"

    guard let resourceUrl = (bundle ?? Bundle(for: Self.self)).url(forResource: resourceName, withExtension: resourceExtension) else {
      throw BundledResourceError.resourceNotFound(resourceName + "." + resourceExtension)
    }

    let data = try Data(contentsOf: resourceUrl)
    let result = try JSONDecoder().decode(T.self, from: data)
    return result
  }
}
