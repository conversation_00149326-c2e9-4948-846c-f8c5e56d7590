import AVFoundation
import Foundation

// MARK: - AVCaptureDevicePermissionServicing

public protocol AVCaptureDevicePermissionServicing {
  func continueIfAllowed(for mediaType: AVMediaType) async -> Bool
  func requestPermissionIfNeeded(for mediaType: AVMediaType) async -> Bool
  func authorizationStatus(for mediaType: AVMediaType) -> AVAuthorizationStatus
}

// MARK: - AVCaptureDevicePermissionService

public struct AVCaptureDevicePermissionService: AVCaptureDevicePermissionServicing {
  private let requestPermission: (AVMediaType) async -> Bool
  private let authorizationStatus: (AVMediaType) -> AVAuthorizationStatus

  public init(
    requestPermission: @escaping (AVMediaType) async -> Bool = AVCaptureDevice.requestAccess(for:),
    authorizationStatus: @escaping (AVMediaType) -> AVAuthorizationStatus = AVCaptureDevice.authorizationStatus(for:)
  ) {
    self.requestPermission = requestPermission
    self.authorizationStatus = authorizationStatus
  }

  public func continueIfAllowed(for mediaType: AVMediaType) async -> Bool {
    await requestPermissionIfNeeded(for: mediaType)
  }

  public func requestPermissionIfNeeded(for mediaType: AVMediaType) async -> Bool {
    switch authorizationStatus(for: mediaType) {
    case .notDetermined:
      return await requestPermission(mediaType)
    case .restricted:
      return true
    case .denied:
      return false
    case .authorized:
      return true
    @unknown default:
      return false
    }
  }

  public func authorizationStatus(for mediaType: AVMediaType) -> AVAuthorizationStatus {
    authorizationStatus(mediaType)
  }
}
