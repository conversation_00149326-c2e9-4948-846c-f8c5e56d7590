// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "<PERSON>GCore",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "<PERSON>GC<PERSON>",
      targets: ["OGCore"]
    ),
    .library(
      name: "OGCoreTestsUtils",
      targets: ["OGCoreTestsUtils"]
    )
  ],
  dependencies: [
    .package(path: "Packages/OGAppEnvironment"),
    .package(path: "Packages/OGStorage"),
    .package(path: "Packages/OGIdentifier"),
    .package(path: "Packages/OGLogger"),
    .package(path: "Packages/OGMacros"),
    .package(path: "Packages/OGMock"),
    .package(path: "Packages/OGZipArchiver"),
    .package(path: "../OGExternalDependencies/OGDIService"),
    .package(path: "../OGExternalDependencies/OGSwiftConcurrencyExtras")
  ],
  targets: [
    .target(
      name: "OGCore",
      dependencies: [
        "OGAppEnvironment",
        "OGStorage",
        "OGIdentifier",
        "OGLogger",
        "OGDIService",
        "OGMacros",
        "OGZipArchiver"
      ]
    ),
    .target(
      name: "OGCoreTestsUtils",
      dependencies: [
        "OGCore",
        .product(name: "OGIdentifierTestsUtils", package: "OGIdentifier"),
        "OGMacros",
        "OGMock",
        "OGSwiftConcurrencyExtras"
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "OGCoreTests",
      dependencies: ["OGCoreTestsUtils"]
    )
  ]
)
