import Foundation
import OGCore
import OGMacros
import OGMock

// MARK: - OGStorageMock

@OGMock
public final class OGFileStorageMock: OGFileStoring {
  public init() {}

  public func contentsOfDirectory(at url: URL) async throws -> [String] {
    try await mock.contentsOfDirectory(at: url)
  }

  public func delete(url: URL) async throws {
    try await mock.delete(url: url)
  }

  public func save(
    _ data: any DataWriting,
    folderUrl: URL,
    fileName: String
  ) async throws {
    try await mock.save(data, folderUrl: folderUrl, fileName: fileName)
  }
}
