import Combine
import OGLogger

public final class OGLogReceiverMock: OGLogReceivable, Equatable {
  public static func == (lhs: OGLogReceiverMock, rhs: OGLogReceiverMock) -> Bool {
    lhs.logDistributer.logPublisher.description ==
      rhs.logDistributer.logPublisher.description
  }

  public init() {}

  public var logDistributer: OGLogDistributable = OGLoggingDistributor()

  public func didReceiveLog(_: OGLogger.OGLog) {}

  public var cancellables: Set<AnyCancellable> = []
}
