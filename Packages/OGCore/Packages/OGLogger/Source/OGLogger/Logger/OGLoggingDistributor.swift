import Combine
import Foundation

// MARK: - OGLoggingDistributable

public protocol OGLoggingDistributable: OGLoggable, OGLogDistributable, ObservableObject {}

// MARK: - OGLoggingDistributor

public final class OGLoggingDistributor: OGLoggingDistributable {
  private let logSubject = PassthroughSubject<OGLog, Never>()
  public var logPublisher: AnyPublisher<OGLog, Never> {
    logSubject.eraseToAnyPublisher()
  }

  public init() {}

  public func log(_ logEntry: OGLog) {
    logSubject.send(logEntry)
  }
}
