import Foundation

// MARK: - OGLogLevel

public enum OGLogLevel: String, CaseIterable {
  case debug
  case warning
  case critical

  public var formatted: String {
    switch self {
    case .critical:
      return "🚨 [\(rawValue)]"
    case .warning:
      return "⚠️ [\(rawValue)]"
    case .debug:
      return "🐛 [\(rawValue)]"
    }
  }

  public var priority: Int {
    switch self {
    case .critical: 1
    case .warning: 2
    case .debug: 3
    }
  }
}

// MARK: - OGLogDomain

public enum OGLogDomain: String {
  case api
  case authentication
  case decoding
  case persistency
  case state
  case service
  case tracking
  case webbridge
}

// MARK: - OGLogContext

public struct OGLogContext: Equatable {
  public let fileName: String
  public let function: String
  public let lineNumber: UInt
  public let timestamp: Date

  public init(file: String, function: String, lineNumber: UInt) {
    self.fileName = Self.extractFileName(from: file)
    self.function = function
    self.lineNumber = lineNumber
    self.timestamp = Date()
  }
}

extension OGLogContext {
  private static func extractFileName(from file: String) -> String {
    file.components(separatedBy: "/")
      .last(where: { !$0.isEmpty })?
      .replacingOccurrences(of: ".swift", with: "")
      ?? "UNKNOWN"
  }
}

// MARK: - OGLoggable

public protocol OGLoggable {
  func log(_ log: OGLog)
}

/// convenience
extension OGLoggable {
  public func log(
    _ level: OGLogLevel,
    domain: OGLogDomain,
    message: String,
    context: OGLogContext
  ) {
    let logEntry = OGLog(level: level, domain: domain, message: message, context: context)
    log(logEntry)
  }

  public func log(
    _ level: OGLogLevel,
    domain: OGLogDomain,
    message: String,
    file: String = #file,
    function: String = #function,
    line: UInt = #line
  ) {
    let context = OGLogContext(file: file, function: function, lineNumber: line)
    log(level, domain: domain, message: message, context: context)
  }

  public func debug(
    domain: OGLogDomain,
    message: String,
    file: String = #file,
    function: String = #function,
    line: UInt = #line
  ) {
    log(
      .debug,
      domain: domain,
      message: message,
      context: OGLogContext(file: file, function: function, lineNumber: line)
    )
  }

  public func warning(
    domain: OGLogDomain,
    message: String,
    file: String = #file,
    function: String = #function,
    line: UInt = #line
  ) {
    log(
      .warning,
      domain: domain,
      message: message,
      context: OGLogContext(file: file, function: function, lineNumber: line)
    )
  }

  public func critical(
    domain: OGLogDomain,
    message: String,
    file: String = #file,
    function: String = #function,
    line: UInt = #line
  ) {
    log(
      .critical,
      domain: domain,
      message: message,
      context: OGLogContext(file: file, function: function, lineNumber: line)
    )
  }
}
