import Combine
import Foundation
import OSLog

extension Logger {
  private static var subsystem = Bundle.main.bundleIdentifier!
  static let appState = Logger(subsystem: subsystem, category: "appstate")
  static let viewCycle = Logger(subsystem: subsystem, category: "viewcycle")
  static let networking = Logger(subsystem: subsystem, category: "networking")
  static let unspecified = Logger(subsystem: subsystem, category: "unspecified")
}

extension OGLogDomain {
  func mapToCategorizedLogger() -> Logger {
    switch self {
    case .api, .decoding:
      return .networking
    case .state:
      return .appState
    default:
      return .unspecified
    }
  }
}

// MARK: - OGConsoleLogger

public final class OGConsoleLogger: OGLogReceivable {
  public let logDistributer: OGLogDistributable
  public var cancellables: Set<AnyCancellable>

  let identifier = "CLOG"

  public func didReceiveLog(_ log: OGLog) {
    let loggingCategory = log.domain.mapToCategorizedLogger()
    let context = "\(log.level.formatted) [\(identifier)] [\(log.context.fileName) > \(log.context.function) > \(log.context.lineNumber)]:"
    switch log.level {
    case .critical:
      loggingCategory.critical("\(context) {\(log.message)}")
    case .warning:
      loggingCategory.warning("\(context) {\(log.message)}")
    case .debug:
      loggingCategory.debug("\(context) {\(log.message, privacy: .private)}")
    }
  }

  public init(logDistributable: OGLogDistributable) {
    self.logDistributer = logDistributable
    self.cancellables = []
    receiveAllLogs()
  }
}
