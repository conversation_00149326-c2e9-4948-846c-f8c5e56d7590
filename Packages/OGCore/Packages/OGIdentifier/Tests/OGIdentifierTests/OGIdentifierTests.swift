import OGIdentifier
import XCTest

final class OGIdentifierTests: XCTestCase {
  override class func setUp() {
    super.setUp()
  }

  override class func tearDown() {
    super.tearDown()
  }

  override func setUpWithError() throws {
    try super.setUpWithError()
  }

  override func tearDownWithError() throws {
    try super.tearDownWithError()
  }

  func test_Identifier_init_doesThrowForEmptyString() throws {
    XCTAssertThrowsError(try OGIdentifier(""))
  }

  func test_Identifier_init_doesThrowForWhitespace() throws {
    XCTAssertThrowsError(try OGIdentifier(" "))
  }

  func test_Identifier_init_returnsNilIfSingleCharacter() throws {
    let sut = try OGIdentifier("a")
    XCTAssertEqual(sut.value, "a")
  }

  func test_Identifier_init_returnsIfDoubleCharacter() throws {
    let sut = try OGIdentifier("ab")
    XCTAssertEqual(sut.value, "ab")
  }

  func test_Identifier_init_returnsIfCombinationOfLetterAndNumber() throws {
    let sut = try OGIdentifier("feature1")
    XCTAssertEqual(sut.value, "feature1")
  }
}
