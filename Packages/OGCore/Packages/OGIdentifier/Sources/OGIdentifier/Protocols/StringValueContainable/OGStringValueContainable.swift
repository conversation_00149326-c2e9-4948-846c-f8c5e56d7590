import Foundation

// MARK: - OGStringValueContainable

/// A protocol that represents a type that can be initialized from a string value.
public protocol OGStringValueContainable {
  /// The string value of the object.
  var value: String { get }
  /// Initializes an object with a string value.
  ///
  /// - Parameter value: The string value to initialize the object with.
  /// - Throws: An error if the string value is invalid.
  init(_ value: String) throws
}

extension OGStringValueContainable {
  /// Initializes an object with a string value.
  ///
  /// - Parameter value: The string value to initialize the object with.
  public init?(with value: String) {
    try? self.init(value)
  }

  /// Initializes an object from a `Decoder`.
  ///
  /// - Parameter decoder: The decoder to use to initialize the object.
  /// - Throws: An error if the object cannot be initialized from the decoder.
  public init(from decoder: Decoder) throws {
    let container = try decoder.singleValueContainer()
    let value = try container.decode(String.self)
    try self.init(value)
  }

  /// Returns an error indicating that the given value is invalid.
  ///
  /// - Parameter value: The invalid value.
  /// - Returns: An error indicating that the given value is invalid.
  public static func decodingError(for value: String) -> Error {
    DecodingError.dataCorrupted(DecodingError.Context(codingPath: [], debugDescription: "Invalid value: \(value)"))
  }
}
