// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "OGStorage",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGStorage",
      targets: ["OGStorage"]
    ),
    .library(
      name: "OGStorageTestsUtils",
      targets: ["OGStorageTestsUtils"]
    )
  ],
  dependencies: [
    .package(
      name: "OGMock",
      path: "../OGMock"
    ),
    .package(
      name: "OGMacros",
      path: "../OGMacros"
    )
  ],
  targets: [
    .target(
      name: "OGStorage"
    ),
    .target(
      name: "OGStorageTestsUtils",
      dependencies: [
        "OGStorage",
        "OGMacros",
        "OGMock"
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "OGStorageTests",
      dependencies: [
        "OGStorageTestsUtils"
      ]
    )
  ]
)
