import Foundation
import OGMacros
import OGMock
import OGStorage

@OGMock
public final class FileManagerMock: FileManaging {
  public init() {}

  public func createDirectory(
    atPath path: String,
    withIntermediateDirectories createIntermediates: Bool,
    attributes: [FileAttributeKey: Any]?
  ) throws {
    try mock.createDirectory(
      atPath: path,
      withIntermediateDirectories: createIntermediates,
      attributes: attributes
    )
  }

  public func contentsOfDirectory(atPath path: String) throws -> [String] {
    try mock.contentsOfDirectory(atPath: path)
  }

  public func fileExists(atPath path: String) -> Bool {
    mock.fileExists(atPath: path)
  }

  public func removeItem(atPath path: String) throws {
    try mock.removeItem(atPath: path)
  }

  public func urls(
    for directory: FileManager.SearchPathDirectory,
    in domainMask: FileManager.SearchPathDomainMask
  ) -> [URL] {
    mock.urls(for: directory, in: domainMask)
  }
}
