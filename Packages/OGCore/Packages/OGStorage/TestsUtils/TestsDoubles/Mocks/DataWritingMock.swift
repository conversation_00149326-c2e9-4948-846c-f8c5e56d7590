import Foundation
import OGMacros
import OGMock
import OGStorage

// MARK: - DataMock

@OGMock
public final class DataMock: DataWriting {
  private let identifier = UUID()

  public init() {}

  public func write(to url: URL, options: Data.WritingOptions) throws {
    try mock.write(to: url, options: options)
  }
}

// MARK: Equatable

extension DataMock: Equatable {
  public static func == (lhs: DataMock, rhs: DataMock) -> Bool {
    lhs.identifier == rhs.identifier
  }
}
