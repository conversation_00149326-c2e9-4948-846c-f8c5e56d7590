import Foundation

// MARK: - OGKeyValueStorable

/// A protocol that defines methods for storing and retrieving values using a key-value interface.
public protocol OGKeyValueStorable: OGBoolStorable {
  /// Returns the value associated with the given key, if it exists.
  ///
  /// - Parameters:
  ///   - key: The key for which to retrieve the associated value.
  /// - Returns: The value associated with the given key, or `nil` if no value is associated with the key.
  func object(forKey key: String) -> Any?

  /// Associates the given value with the given key.
  ///
  /// - Parameters:
  ///   - value: The value to associate with the key.
  ///   - key: The key to use for storing the value.
  func set(_ value: Any?, forKey key: String)

  /// Removes the value associated with the given key from the store.
  ///
  /// - Parameters:
  ///   - key: The key whose value should be removed from the store.
  func removeObject(forKey key: String)
}

// MARK: - OGBoolStorable

public protocol OGBoolStorable {
  /// Returns the value associated with the given key, if it exists.
  ///
  /// - Parameters:
  ///   - key: The key for which to retrieve the associated value.
  /// - Returns: The boolean value associated with the given key, or `nil` if no value is associated with the key.
  func bool(forKey key: String) -> Bool

  /// Associates the given value with the given key.
  ///
  /// - Parameters:
  ///   - value: The boolean value to associate with the key.
  ///   - key: The key to use for storing the value.
  func set(_ value: Bool, forKey key: String)
}
