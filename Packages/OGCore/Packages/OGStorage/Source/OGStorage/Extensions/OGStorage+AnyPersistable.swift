import Foundation

// MARK: - OGStorage + AnyPersistable

extension OGStorage: AnyPersistable {
  /// Persists a value of type `Any` for the given key.
  ///
  /// - Parameters:
  ///   - value: The value to persist.
  ///   - key: The key to use for storing the value.
  public func persist(value: Any, forKey key: RawValueable) {
    keyValueStorage.set(value, forKey: key.rawValue)
  }

  /// Returns the value associated with the given key, if it exists.
  ///
  /// - Parameters:
  ///   - key: The key for which to retrieve the associated value.
  /// - Returns: The value associated with the given key, or `nil` if no value is associated with the key.
  public func value<T>(forKey key: RawValueable) -> T? {
    keyValueStorage.object(forKey: key.rawValue) as? T
  }

  /// Removes the value associated with the given key from the store.
  ///
  /// - Parameters:
  ///   - key: The key whose value should be removed from the store.
  public func delete(valueForKey key: RawValueable) {
    keyValueStorage.removeObject(forKey: key.rawValue)
  }
}

// MARK: - BoolPersistable

extension OGStorage {
  /// Returns the boolean value associated with the given key, false if it does not exists.
  ///
  /// - Parameters:
  ///   - key: The key for which to retrieve the associated value.
  /// - Returns: The boolean value associated with the given key, or `false` if no value is associated with the key.
  public func bool(forKey key: RawValueable) -> Bool {
    keyValueStorage.object(forKey: key.rawValue) as? Bool ?? false
  }

  /// Persists a value of type `Bool` for the given key.
  ///
  /// - Parameters:
  ///   - value: The boolean value to persist.
  ///   - key: The key to use for storing the value.
  public func persistBool(_ value: Bool, forKey key: RawValueable) {
    keyValueStorage.set(value, forKey: key.rawValue)
  }
}
