import Foundation

// MARK: - FileManaging

public protocol FileManaging {
  func createDirectory(
    atPath path: String,
    withIntermediateDirectories createIntermediates: Bool,
    attributes: [FileAttributeKey: Any]?
  ) throws
  func contentsOfDirectory(atPath path: String) throws -> [String]
  func fileExists(atPath path: String) -> Bool
  func removeItem(atPath path: String) throws
  func urls(
    for directory: FileManager.SearchPathDirectory,
    in domainMask: FileManager.SearchPathDomainMask
  ) -> [URL]
}

// MARK: - FileManager + FileManaging

extension FileManager: FileManaging {}
