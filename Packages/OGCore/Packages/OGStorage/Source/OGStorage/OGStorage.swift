import Foundation

/// Stores values in a local storage.
public struct OGStorage {
  /// Store for the key value storage.
  let keyValueStorage: OGKeyValueStorable

  /// - Parameters:
  ///   - storage: The object to use for storing values in the cache. Defaults to `UserDefaults.standard`.
  public init(keyValueStorage: OGKeyValueStorable = UserDefaults.standard) {
    self.keyValueStorage = keyValueStorage
  }
}
