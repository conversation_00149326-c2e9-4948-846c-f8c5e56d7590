import Foundation

// MARK: - OGFileStoring

public protocol OGFileStoring {
  func contentsOfDirectory(
    at url: URL
  ) async throws -> [String]
  func delete(url: URL) async throws
  func save(
    _ data: any DataWriting,
    folderUrl: URL,
    fileName: String
  ) async throws
}

// MARK: - OGFileStorage

public actor OGFileStorage: OGFileStoring {
  public enum FileStorageError: Error {
    case fileNotFound
  }

  private let fileManager: FileManaging

  public init(
    fileManager: FileManaging = FileManager.default
  ) {
    self.fileManager = fileManager
  }

  public func contentsOfDirectory(
    at url: URL
  ) async throws -> [String] {
    try fileManager.contentsOfDirectory(atPath: url.path)
  }

  public func save(
    _ data: any DataWriting,
    folderUrl: URL,
    fileName: String
  ) async throws {
    try createFolder(at: folderUrl)

    let fileUrl = URL(
      fileURLWithPath: folderUrl
        .appendingPathComponent(fileName)
        .absoluteString
    )

    try data.write(to: fileUrl)
  }

  public func delete(url: URL) async throws {
    guard
      fileManager.fileExists(atPath: url.path)
    else { throw FileStorageError.fileNotFound }
    try fileManager.removeItem(atPath: url.path)
  }

  private func createFolder(at url: URL) throws {
    try fileManager.createDirectory(
      atPath: url.absoluteString,
      withIntermediateDirectories: true,
      attributes: nil
    )
  }
}

// MARK: - DataWriting

public protocol DataWriting: Equatable {
  func write(to url: URL, options: Data.WritingOptions) throws
}

extension DataWriting {
  func write(to url: URL) throws {
    try write(to: url, options: [])
  }
}

// MARK: - Data + DataWriting

extension Data: DataWriting {}
