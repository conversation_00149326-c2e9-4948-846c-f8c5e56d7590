import Foundation
import ZipArchive

// MARK: - OGZipArchiving

public protocol OGZipArchiving {
  func unzip(fileUrl: URL, folderUrl: URL) async throws
}

// MARK: - OGZipArchiver

public actor OGZipArchiver: OGZipArchiving {
  public enum ZipArchiveError: Error {
    case unzip
  }

  public init() {}

  public func unzip(
    fileUrl: URL,
    folderUrl: URL
  ) async throws {
    try await withCheckedThrowingContinuation { continuation in
      unzip(
        fileUrl: fileUrl,
        folderUrl: folderUrl
      ) { result in
        switch result {
        case .success:
          continuation.resume()
        case let .failure(error):
          continuation.resume(throwing: error)
        }
      }
    }
  }

  private func unzip(
    fileUrl: URL,
    folderUrl: URL,
    completion: @escaping (Result<Bool, Error>) -> Void
  ) {
    SSZipArchive
      .unzipFile(
        atPath: fileUrl.path,
        toDestination: folderUrl.path,
        progressHandler: { _, _, _, _ in },
        completionHandler: { _, success, error in

          if let error {
            completion(.failure(error))
          } else if success {
            completion(.success(success))
          } else {
            completion(.failure(ZipArchiveError.unzip))
          }
        }
      )
  }
}
