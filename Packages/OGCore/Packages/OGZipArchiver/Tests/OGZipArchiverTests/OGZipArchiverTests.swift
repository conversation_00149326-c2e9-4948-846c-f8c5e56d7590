import Foundation
@testable import OGZipArchiver
import XCTest

// MARK: - OGZipArchiverTests

final class OGZipArchiverTests: XCTestCase {
  func test_unzip_file() async throws {
    let sut = OGZipArchiver()
    guard
      let zipFile = Bundle.module.url(forResource: "test", withExtension: "zip")
    else {
      XCTFail("Could not find test.zip")
      return
    }
    let folderUrl = FileManager.default.temporaryDirectory
    try await sut.unzip(fileUrl: zipFile, folderUrl: folderUrl)
    XCTAssertTrue(
      FileManager.default.fileExists(atPath: folderUrl.appendingPathComponent("test.txt").path)
    )
  }
}
