import SwiftSyntax

struct MockedVariableDeclFactory {
  func make(typeName: TokenSyntax, from _: ClassDeclSyntax) -> VariableDeclSyntax {
    VariableDeclSyntax(
      bindingSpecifier: .keyword(.public),
      bindings:
      PatternBindingListSyntax(
        arrayLiteral:
        PatternBindingSyntax(
          pattern: PatternSyntax(stringLiteral: "let mock"),
          initializer: InitializerClauseSyntax(value: ExprSyntax(stringLiteral: "\(typeName)()"))
        )
      )
    )
  }
}
