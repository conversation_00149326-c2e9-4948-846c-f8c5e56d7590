import Foundation
import SwiftSyntax
import SwiftSyntaxBuilder

// MARK: - FunctionMockableDeclarationFactory

struct FunctionMockableDeclarationFactory {
  @MemberBlockItemListBuilder
  func callTrackerDeclarations(_ functions: [FunctionDeclSyntax], nameBuilder: NameBuilder) -> MemberBlockItemListSyntax {
    for function in functions {
      let className = nameBuilder.createClassName(function: function)
      let varName = nameBuilder.createVarName(function: function)
      VariableDeclSyntax(
        modifiers: DeclModifierListSyntax([.init(name: .identifier(""))]),
        Keyword.public,
        name: PatternSyntax(stringLiteral: "var \(varName) = \(className)()")
      )
    }
  }

  @MemberBlockItemListBuilder
  func varMockStruct(_ functions: [FunctionDeclSyntax], nameBuilder: NameBuilder) -> MemberBlockItemListSyntax {
    for function in functions {
      let params = function.signature.parameterClause.parameters
        .map(\.type)
        .map { type in
          GenericParameterSyntax(name: TokenSyntax(stringLiteral: type.description))
        }
      let returnType = function.signature.returnClause?.type.description.trimmingCharacters(in: .whitespacesAndNewlines) ?? "Void"
      let throwType = function.signature.effectSpecifiers?.throwsSpecifier?.text

      let voidOne = GenericParameterSyntax(name: TokenSyntax(stringLiteral: "Void"))

      let pa = GenericParameterListSyntax(params.isEmpty ? [voidOne] : params).map { p in
        p.description
      }.joined(separator: ", ")
      let className = nameBuilder.createClassName(function: function)
      VariableDeclSyntax(
        modifiers: DeclModifierListSyntax([.init(name: .identifier(""))]),
        .public,
        name: PatternSyntax(stringLiteral: mockStruct(name: className, argumentType: "(\(pa))", returnType: returnType, canThrow: throwType != nil))
      )
    }
  }

  @MemberBlockItemListBuilder
  func mockImplementations(for functions: [FunctionDeclSyntax], nameBuilder: NameBuilder) -> MemberBlockItemListSyntax {
    for function in functions {
      let paramsValues = function.signature.parameterClause.parameters
        .map {
          $0.secondName?.text != nil ? "\($0.secondName!.text) as \($0.type.description)" : "\($0.firstName.text) as \($0.type.description)"
        }

      let returnType = function.signature.returnClause?.type.description.trimmingCharacters(in: .whitespacesAndNewlines)
      let varName = nameBuilder.createVarName(function: function)
      let throwType = function.signature.effectSpecifiers?.throwsSpecifier?.text
      FunctionDeclSyntax(
        attributes: function.attributes,
        modifiers: function.modifiers,
        funcKeyword: function.funcKeyword,
        name: function.name,
        genericParameterClause: function.genericParameterClause,
        signature: function.signature,
        genericWhereClause: function.genericWhereClause
      ) {
        if let returnType, throwType != nil {
          CodeBlockItemSyntax(stringLiteral: "try \(varName).record((\(paramsValues.joined(separator: ", ")))) as \(returnType)")
        } else if let returnType {
          CodeBlockItemSyntax(stringLiteral: "\(varName).record((\(paramsValues.joined(separator: ", ")))) as \(returnType)")
        } else if throwType != nil {
          CodeBlockItemSyntax(stringLiteral: "try \(varName).record((\(paramsValues.joined(separator: ", "))))")
        } else {
          CodeBlockItemSyntax(stringLiteral: "let _ = \(varName).record((\(paramsValues.joined(separator: ", "))))")
        }
      }
    }
  }
}

extension FunctionMockableDeclarationFactory {
  func mockStruct(name: String, argumentType: String, returnType: String, canThrow: Bool) -> String {
    """
    struct \(name) {
          public typealias CallType = (\(argumentType)) -> \(returnType)
          public var fulfilled: (() -> Void)?
          private var callMock: CallType?
          private var callMockError: Error?
          public var latestCall: \(argumentType)? {
           callsHistory.last
          }

          public var callsCount: Int {
            callsHistory.count
          }

          public mutating func mockCall(_ mock: @escaping CallType) {
            callMock = mock
          }

          public mutating func mockThrowCall(_ error: Error) {
            callMockError = error
          }

          public private(set) var callsHistory: [\(argumentType)] = []

          public mutating func record(_ arguments: \(argumentType)) \(canThrow ? "throws" : "") \(returnType == "Void" ? "" : "-> \(returnType)") {
            callsHistory.append(arguments)
            fulfilled?()
           \(canThrow ? " if callMockError != nil { throw callMockError! }" : "")
           \(returnType == "Void" ? "callMock?(arguments)" : "return callMock!(arguments)")
          }
        }
    """
  }
}

extension String {
  var capitalizedSentence: String {
    let firstLetter = prefix(1).capitalized
    let remainingLetters = dropFirst().lowercased()
    return firstLetter + remainingLetters
  }
}
