import SwiftSyntax
import SwiftSyntaxMacros

public struct SwiftMocksMacro: MemberMacro {
  public static func expansion(
    of _: AttributeSyntax,
    providingMembersOf declaration: some DeclGroupSyntax,
    in _: some MacroExpansionContext
  ) throws -> [DeclSyntax] {
    guard
      let classDecl = declaration.as(ClassDeclSyntax.self)
    else { return [] }
    let stackIdentifier = TokenSyntax(stringLiteral: classDecl.name.text + "OGMock")
    return [
      DeclSyntax(MockedVariableDeclFactory().make(typeName: stackIdentifier, from: classDecl)),
      DeclSyntax(MockedClassDeclFactory().make(typeName: stackIdentifier, from: classDecl))
    ]
  }
}
