// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.
import CompilerPluginSupport
import PackageDescription

let package = Package(
  name: "OGMacros",
  platforms: [.iOS(.v15), .macOS(.v12)],
  products: [
    .library(
      name: "OGMac<PERSON>",
      targets: ["OGMacros"]
    )
  ],
  dependencies: [
    .package(
      url: "https://github.com/apple/swift-syntax.git",
      exact: "600.0.1"
    ),
    .package(path: "../OGIdentifier")
  ],
  targets: [
    .macro(
      name: "OGIdentifierMacros",
      dependencies: [
        .product(name: "SwiftSyntaxMacros", package: "swift-syntax"),
        .product(name: "SwiftCompilerPlugin", package: "swift-syntax")
      ]
    ),
    .macro(
      name: "OGMocksMacros",
      dependencies: [
        .product(name: "SwiftSyntaxMacros", package: "swift-syntax"),
        .product(name: "SwiftCompilerPlugin", package: "swift-syntax")
      ]
    ),
    .target(
      name: "<PERSON>GMac<PERSON>",
      dependencies: ["OGIdentifierMacros", "OGMocksMacros", "OGIdentifier"]
    ),
    .testTarget(
      name: "OGMacrosTests",
      dependencies: [
        "OGMacros",
        .product(name: "SwiftSyntaxMacrosTestSupport", package: "swift-syntax")
      ]
    )
  ]
)
