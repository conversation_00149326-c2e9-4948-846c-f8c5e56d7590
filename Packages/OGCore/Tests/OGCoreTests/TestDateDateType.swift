import XCTest

@testable import OGCore

class TestDateDateType: XCTestCase {
  func testGetDateType() {
    let calendar = Calendar.current

    let currentDate = calendar.date(from: DateComponents(year: 2_018, month: 4, day: 30))!

    guard let yesterday = calendar.date(byAdding: .day, value: -1, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -1, to: currentDate) date")
      return
    }

    guard let twoDaysAgo = calendar.date(byAdding: .day, value: -2, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -2, to: currentDate) date")
      return
    }
    guard let sixDaysAgo = calendar.date(byAdding: .day, value: -6, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -6, to: currentDate) date")
      return
    }
    guard let sevenDaysAgo = calendar.date(byAdding: .day, value: -7, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -7, to: currentDate) date")
      return
    }
    guard let eightDaysAgo = calendar.date(byAdding: .day, value: -8, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -8, to: currentDate) date")
      return
    }
    guard let lastMonth = calendar.date(byAdding: .day, value: -30, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -30, to: currentDate) date")
      return
    }
    guard let lastYear = calendar.date(byAdding: .day, value: -365, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -365, to: currentDate) date")
      return
    }

    XCTAssertEqual(currentDate.getDateType(currentDate), .today)
    XCTAssertEqual(yesterday.getDateType(currentDate), .yesterday)
    XCTAssertEqual(twoDaysAgo.getDateType(currentDate), .inLastWeek)
    XCTAssertEqual(sixDaysAgo.getDateType(currentDate), .inLastWeek)
    XCTAssertEqual(sevenDaysAgo.getDateType(currentDate), .olderThanOneWeek)
    XCTAssertEqual(eightDaysAgo.getDateType(currentDate), .olderThanOneWeek)
    XCTAssertEqual(lastMonth.getDateType(currentDate), .olderThanOneWeek)
    XCTAssertEqual(lastYear.getDateType(currentDate), .inLastYear)
  }

  func testGetDateTypeNewYear() {
    let calendar = Calendar.current

    let currentDate = calendar.date(from: DateComponents(year: 2_019, month: 1, day: 3))!

    guard let yesterday = calendar.date(byAdding: .day, value: -1, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -1, to: currentDate) date")
      return
    }

    guard let twoDaysAgo = calendar.date(byAdding: .day, value: -2, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -2, to: currentDate) date")
      return
    }
    guard let sixDaysAgo = calendar.date(byAdding: .day, value: -6, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -6, to: currentDate) date")
      return
    }
    guard let sevenDaysAgo = calendar.date(byAdding: .day, value: -7, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -7, to: currentDate) date")
      return
    }
    guard let eightDaysAgo = calendar.date(byAdding: .day, value: -8, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -8, to: currentDate) date")
      return
    }
    guard let lastMonth = calendar.date(byAdding: .day, value: -30, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -30, to: currentDate) date")
      return
    }
    guard let lastYear = calendar.date(byAdding: .day, value: -365, to: currentDate) else {
      XCTFail("couldn't generate calendar.date(byAdding: .day, value: -365, to: currentDate) date")
      return
    }

    XCTAssertEqual(currentDate.getDateType(currentDate), .today)
    XCTAssertEqual(yesterday.getDateType(currentDate), .yesterday)
    XCTAssertEqual(twoDaysAgo.getDateType(currentDate), .inLastWeek)
    XCTAssertEqual(sixDaysAgo.getDateType(currentDate), .inLastWeek)
    XCTAssertEqual(sevenDaysAgo.getDateType(currentDate), .inLastYear)
    XCTAssertEqual(eightDaysAgo.getDateType(currentDate), .inLastYear)
    XCTAssertEqual(lastMonth.getDateType(currentDate), .inLastYear)
    XCTAssertEqual(lastYear.getDateType(currentDate), .inLastYear)
  }
}
