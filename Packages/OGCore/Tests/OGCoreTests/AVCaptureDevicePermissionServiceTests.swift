import AVFoundation
import OGCoreTestsUtils
import XCTest

@testable import OGCore

final class AVCaptureDevicePermissionServiceTests: XCTestCase {
  func test_WHEN_requestPermissionIfNeeded_AND_notDetermined_callAuthorizationStatus_AND_callRequestPermission() async throws {
    let captureDevice = MockCaptureDevice()

    captureDevice.mock.mockAuthorizationStatusCalls.mockCall { mediaType in
      XCTAssertEqual(mediaType, .video)
      return .notDetermined
    }

    captureDevice.mock.mockRequestPermissionCalls.mockCall { mediaType in
      XCTAssertEqual(mediaType, .video)
      return true
    }

    let sut = AVCaptureDevicePermissionService(requestPermission: captureDevice.mockRequestPermission(mediaType:), authorizationStatus: captureDevice.mockAuthorizationStatus(mediaType:))

    let result = await sut.requestPermissionIfNeeded(for: .video)

    XCTAssertTrue(result)
    XCTAssertEqual(captureDevice.mock.mockAuthorizationStatusCalls.callsCount, 1)
    XCTAssertEqual(captureDevice.mock.mockRequestPermissionCalls.callsCount, 1)
  }
}
