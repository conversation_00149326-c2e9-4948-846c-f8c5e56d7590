import Foundation

public struct DialogBehavior: Codable, Equatable, Sendable {
  public enum PreCondition: String, Codable, Sendable {
    case pushDisabled
    case none
  }

  let id: String
  let maxInvocations: Int?
  let minutesBetweenInvocations: Int?
  let precondition: PreCondition
  let conditions: [DialogCondition]
  let disabledUrls: [String]
  let action: DialogAction

  public init(
    id: String,
    maxInvocations: Int?,
    preCondition: PreCondition,
    conditions: [DialogCondition],
    action: DialogAction,
    disabledUrls: [String],
    minutesBetweenInvocations: Int?
  ) {
    self.id = id
    self.maxInvocations = maxInvocations
    self.precondition = preCondition
    self.conditions = conditions
    self.action = action
    self.disabledUrls = disabledUrls
    self.minutesBetweenInvocations = minutesBetweenInvocations
  }

  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    self.id = try container.decode(String.self, forKey: .id)
    self.maxInvocations = try container.decodeIfPresent(Int.self, forKey: .maxInvocations)
    self.minutesBetweenInvocations = try container.decodeIfPresent(Int.self, forKey: .minutesBetweenInvocations)
    self.conditions = try container.decodeIfPresent([DialogCondition].self, forKey: .conditions) ?? []
    self.action = try container.decode(DialogAction.self, forKey: .action)
    self.disabledUrls = try container.decodeIfPresent([String].self, forKey: .disabledUrls) ?? []
    let precondition = try container.decodeIfPresent(String.self, forKey: .precondition)
    switch precondition {
    case .none:
      self.precondition = .none
    case let .some(value):
      self.precondition = PreCondition(rawValue: value) ?? .none
    }
  }
}
