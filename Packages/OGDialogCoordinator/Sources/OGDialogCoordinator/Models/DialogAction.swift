import Foundation

public struct DialogAction: Codable, Equatable, Sendable {
  public enum ActionType: String, Codable, Sendable {
    case navigation
    case none
  }

  let type: ActionType
  let url: String

  public init(
    type: DialogAction.ActionType,

    url: String
  ) {
    self.type = type
    self.url = url
  }

  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    self.url = try container.decode(String.self, forKey: .url)
    let type = try container.decodeIfPresent(String.self, forKey: .type)
    switch type {
    case .none:
      self.type = .none
    case let .some(value):
      self.type = ActionType(rawValue: value) ?? .none
    }
  }
}
