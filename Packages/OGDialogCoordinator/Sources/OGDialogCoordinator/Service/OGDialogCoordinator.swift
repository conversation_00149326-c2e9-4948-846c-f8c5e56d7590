import Combine
import Foundation
import OGCore
import OGDIService
import OGScreenViewUpdate
import OGSystemKit
import OGWebBridge
import UserNotifications

// MARK: - OGDialogCoordinable

public protocol OGDialogCoordinable {
  func actionFor(behavior: DialogBehavior) -> DialogAction?

  func createWebBridges(for behaviors: [DialogBehavior])

  func hasWebBridgeCallNameFulfillment(
    for behaviors: [DialogBehavior],
    with webBridgeCallName: String
  ) async -> DialogBehavior?

  func hasScreenViewCountFulfillment(
    for behaviors: [DialogBehavior],
    with url: URL
  ) async -> DialogBehavior?

  func hasAppStartCountFulfillment(
    for behaviors: [DialogBehavior]
  ) async -> DialogBehavior?
}

// MARK: - OGDialogCoordinator

final class OGDialogCoordinator: OGDialogCoordinable {
  @OGInjected(\OGDialogCoordinatorContainer.webBridgeActionHandler) private var webBridgeActionHandler
  @OGInjected(\OGWebBridgeContainer.globalWebBridge) private var globalWebBridge
  @OGInjected(\OGCoreContainer.logger) private var logger
  @OGInjected(\OGSystemKitContainer.userNotificationCenter) private var userNotificationCenter

  private let storage: any AnyPersistable

  init(storage: any AnyPersistable = OGCoreContainer.shared.storage()) {
    self.storage = storage
    globalWebBridge.addActionHandler(webBridgeActionHandler)
  }

  func actionFor(behavior: DialogBehavior) -> DialogAction? {
    switch behavior.action.type {
    case .navigation:
      willInvoke(behavior: behavior)
      return behavior.action
    case .none:
      return nil
    }
  }

  func hasWebBridgeCallNameFulfillment(
    for behaviors: [DialogBehavior],
    with webBridgeCallName: String
  ) async
    -> DialogBehavior? {
    let behavior = behaviors
      .first(where: { [weak self] in
        self?.canInvoke(behavior: $0) != nil &&
          $0.conditions
          .contains(where: {
            $0.webBridgeCallName == webBridgeCallName
          })
      })

    let behaviors = behaviors.filter { $0 != behavior }

    if let behavior, await hasPreConditionFulfillment(behavior: behavior) {
      return behavior
    } else if !behaviors.isEmpty, behavior != nil {
      return await hasWebBridgeCallNameFulfillment(for: behaviors, with: webBridgeCallName)
    } else {
      return nil
    }
  }

  func hasScreenViewCountFulfillment(
    for behaviors: [DialogBehavior],
    with url: URL
  ) async
    -> DialogBehavior? {
    await hasScreenViewCountFulfillment(for: behaviors, with: url, updateCount: true)
  }

  func hasAppStartCountFulfillment(
    for behaviors: [DialogBehavior]
  ) async -> DialogBehavior? {
    await hasAppStartCountFulfillment(for: behaviors, updateCount: true)
  }

  func createWebBridges(
    for behaviors: [DialogBehavior]
  ) {
    let conditions = behaviors.reduce([]) { $1.conditions }
    let webBridgeNames = conditions.compactMap(\.webBridgeCallName)
    guard !webBridgeNames.isEmpty else { return }
    webBridgeActionHandler.update(webBridgeCallName: webBridgeNames)
  }

  private func hasScreenViewCountFulfillment(
    for behaviors: [DialogBehavior],
    with url: URL,
    updateCount: Bool
  ) async
    -> DialogBehavior? {
    let allowedBehaviors = behaviors.filter {
      $0.disabledUrls.first(where: { url.absoluteString.range(
        of: $0.hasPrefix("regex:") ? String($0.dropFirst("regex:".count)) : $0,
        options: .regularExpression
      ) != nil }) == nil
    }

    let behaviorsWithCount = allowedBehaviors.map { behavior in
      var count = ((storage.value(forKey: PersistedKey.screenViewCountId(behavior.id)) as? Int) ?? 0)
      if updateCount {
        count += 1
        storage.persist(value: count, forKey: PersistedKey.screenViewCountId(behavior.id))
      }
      return (behavior, count)
    }

    let behavior = hasCountFulfillment(for: behaviorsWithCount, with: .screenViews)
    let behaviors = behaviors.filter { $0 != behavior }

    if let behavior, await hasPreConditionFulfillment(behavior: behavior) {
      return behavior
    } else if !behaviors.isEmpty, behavior != nil {
      return await hasScreenViewCountFulfillment(for: behaviors, with: url, updateCount: false)
    } else {
      return nil
    }
  }

  private func hasAppStartCountFulfillment(
    for behaviors: [DialogBehavior],
    updateCount: Bool = true
  ) async
    -> DialogBehavior? {
    var count = ((storage.value(forKey: PersistedKey.appStartCount) as? Int) ?? 0)

    if updateCount {
      count += 1
      storage.persist(value: count, forKey: PersistedKey.appStartCount)
    }

    let behaviorsWithCount = behaviors.map { behavior in
      (behavior, count)
    }

    let behavior = hasCountFulfillment(for: behaviorsWithCount, with: .appStarts)
    let behaviors = behaviors.filter { $0 != behavior }

    if let behavior, await hasPreConditionFulfillment(behavior: behavior) {
      return behavior
    } else if !behaviors.isEmpty, behavior != nil {
      return await hasAppStartCountFulfillment(for: behaviors, updateCount: false)
    } else {
      return nil
    }
  }

  private func hasPreConditionFulfillment(behavior: DialogBehavior) async -> Bool {
    switch behavior.precondition {
    case .pushDisabled where await isPushEnabled():
      return false
    case .none, .pushDisabled:
      return true
    }
  }

  private func isPushEnabled() async -> Bool {
    let settings = await userNotificationCenter.authorizationStatus()
    return settings == .authorized
  }

  private func canInvoke(behavior: DialogBehavior?) -> DialogBehavior? {
    guard let behavior else {
      return nil
    }

    guard let maxInvocations = behavior.maxInvocations else {
      return behavior
    }

    if let minutesBetweenInvocations = behavior.minutesBetweenInvocations,
       let lastDate = storage.value(forKey: PersistedKey.invocationDateId(behavior.id)) as? Date {
      let diffComponents = Calendar.current.dateComponents([.minute], from: lastDate, to: Date())
      let minutes = diffComponents.minute ?? 0
      if minutes < minutesBetweenInvocations {
        return nil
      }
    }

    let invocationCount = storage.value(forKey: PersistedKey.invocationCountId(behavior.id)) as? Int ?? 0
    if invocationCount <= maxInvocations {
      return behavior
    } else {
      return nil
    }
  }

  private func willInvoke(behavior: DialogBehavior) {
    let invocations = storage.value(forKey: PersistedKey.invocationCountId(behavior.id)) as? Int ?? 0
    storage.persist(value: invocations + 1, forKey: PersistedKey.invocationCountId(behavior.id))
    storage.persist(value: Date(), forKey: PersistedKey.invocationDateId(behavior.id))
    logger.log(.debug, domain: .service, message: "will Invoke behavior: \(behavior.id)")
  }

  private func hasCountFulfillment(
    for behaviors: [(behavior: DialogBehavior, count: Int)],
    with type: DialogCondition.ConditionType
  )
    -> DialogBehavior? {
    behaviors
      .first { [weak self] tuple in
        self?.canInvoke(behavior: tuple.behavior) != nil &&
          tuple.behavior.conditions
          .contains {
            if let start = $0.start,
               $0.period == nil,
               $0.type == type {
              return start == tuple.count
            }
            if let period = $0.period,
               $0.start == nil,
               $0.type == type {
              return tuple.count % period == 0
            }
            guard
              let period = $0.period,
              let start = $0.start,
              $0.type == type
            else { return false }
            return tuple.count % period == 0 || start == tuple.count
          }
      }
      .map(\.behavior)
  }

  enum PersistedKey: RawValueable {
    var rawValue: String {
      switch self {
      case .appStartCount:
        return "dialogCoordinatorAppStartCount"
      case let .screenViewCountId(id):
        return "dialogCoordinatorScreenViewCount\(id)"
      case let .invocationCountId(id):
        return "dialogCoordinatorInvocationCount\(id)"
      case let .invocationDateId(id):
        return "dialogCoordinatorInvocationDate\(id)"
      }
    }

    case appStartCount
    case screenViewCountId(String)
    case invocationCountId(String)
    case invocationDateId(String)
  }
}
