import Combine
import Foundation
import OGWebBridge

// MARK: - OGDialogCoordinatorWebBridgeActionHandlable

public protocol OGDialogCoordinatorWebBridgeActionHandlable: OGWebBridgeActionHandlable {
  var webBridgeCallName: CurrentValueSubject<String?, Never> { get }
  func update(webBridgeCallName: [String])
}

// MARK: - OGDialogCoordinatorWebBridgeActionHandler

final class OGDialogCoordinatorWebBridgeActionHandler: OGDialogCoordinatorWebBridgeActionHandlable {
  private(set) var webBridgeCallName: CurrentValueSubject<String?, Never> = CurrentValueSubject(nil)
  var webBridgeNames: [String] = []

  func update(webBridgeCallName: [String]) {
    webBridgeNames = webBridgeCallName
  }

  func canHandleUpdateAction(_ name: String) -> Bool {
    webBridgeNames.contains(name)
  }

  func handleUpdateAction(_ action: String, data _: [String: Any]) {
    webBridgeCallName.send(action)
  }
}
