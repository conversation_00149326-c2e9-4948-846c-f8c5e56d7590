import Combine
import Foundation
import OGDIService

// MARK: - OGScreenViewUpdatable

public protocol OGScreenViewUpdatable {
  func send(with url: URL?)
  var update: CurrentValueSubject<URL?, Never> { get }
}

// MARK: - OGScreenViewUpdate

public final class OGScreenViewUpdate: OGScreenViewUpdatable {
  public private(set) var update: CurrentValueSubject<URL?, Never> = CurrentValueSubject(nil)
  public func send(with url: URL?) {
    update.send(url)
  }
}

// MARK: - OGScreenViewUpdateContainer

public final class OGScreenViewUpdateContainer: OGDISharedContainer {
  public static var shared: OGScreenViewUpdateContainer = .init()
  public var manager: OGDIContainerManager = .init()

  public var screenViewUpdate: OGDIService<OGScreenViewUpdatable> {
    self {
      OGScreenViewUpdate()
    }.cached
  }
}
