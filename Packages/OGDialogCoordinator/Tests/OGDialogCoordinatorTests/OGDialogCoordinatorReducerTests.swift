@testable import OGDialogCoordinator
import OGDialogCoordinatorTestsUtils
import XCTest

final class OGDialogCoordinatorReducerTests: XCTestCase {
  func test_GIVEN_initialState_WHEN_update_THEN_stateUpdated() {
    var state = OGDialogCoordinatorState.initial
    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._update(.stub)
    )
    XCTAssertEqual(
      OGDialogCoordinatorState(
        isAwaitingUpdate: false,
        debounce: 0.5,
        didNavigateAt: 0.0,
        didStartNavigateAt: 0.0
      ),
      state
    )
  }

  func test_GIVEN_state_WHEN_triggeredBehavior_THEN_stateUpdated() {
    var state = OGDialogCoordinatorState.stub
    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._triggeredBehavior(.stubPush)
    )
    XCTAssertEqual(
      DialogBehavior.stubPush,
      state.triggeredBehavior
    )
    XCTAssertTrue(
      state.willStartNavigateAt > 1
    )
  }

  func test_GIVEN_state_WHEN_navigate_THEN_stateUpdated() {
    var state = OGDialogCoordinatorState.stub
    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._navigate("")
    )
    XCTAssertNil(
      state.triggeredBehavior
    )
    XCTAssertTrue(
      state.didStartNavigateAt > 1
    )
  }
}
