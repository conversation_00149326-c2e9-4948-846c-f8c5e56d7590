import OGCoreTestsUtils
@testable import OGDialogCoordinator
import OGDialogCoordinatorTestsUtils
import OGDIService
import OGScreenViewUpdate
import XCTest

final class OGDialogCoordinatorConnectorTests: XCTestCase {
  var dialogCoordinatorWebBridgeActionHandler: OGDialogCoordinatorWebBridgeActionHandlerMock?
  override func setUpWithError() throws {
    OGDialogCoordinatorFeatureAdapterContainer.shared.coordinator.register {
      FeatureAdapterMock()
    }

    OGDialogCoordinatorContainer.shared.webBridgeActionHandler.register {
      OGDialogCoordinatorWebBridgeActionHandlerMock()
    }

    OGScreenViewUpdateContainer.shared.screenViewUpdate.register {
      ScreenViewUpdateMock()
    }

    try super.setUpWithError()
  }

  override func tearDownWithError() throws {
    OGDialogCoordinatorFeatureAdapterContainer.shared.coordinator.reset()
    OGDialogCoordinatorContainer.shared.webBridgeActionHandler.reset()
    OGScreenViewUpdateContainer.shared.screenViewUpdate.reset()
    try super.tearDownWithError()
  }

  func test_GIVEN_configure_dispatch_update() async throws {
    let expectation = expectation(description: "Expected set event to be received")

    let testActor = TestEventActor<OGDialogCoordinatorAction>()
    let dispatch = { event in
      await testActor.addEvent(event)
      if await testActor.events.count == 1 {
        expectation.fulfill()
      }
    }
    let sut = OGDialogCoordinatorConnector()
    await sut.configure(dispatch: dispatch)
    await fulfillment(of: [expectation], timeout: 0.5)
    let actorActualEvents = await testActor.events
    XCTAssertTrue(actorActualEvents.contains(OGDialogCoordinatorAction._update(.stub)))
  }

  func test_WHEN_screenView_dispatch_receivedScreenView() async throws {
    let expectation = expectation(description: "Expected set event to be received")
    OGScreenViewUpdateContainer.shared.screenViewUpdate().send(with: .stub)
    let testActor = TestEventActor<OGDialogCoordinatorAction>()
    let dispatch = { event in
      await testActor.addEvent(event)
      if await testActor.events.count == 2 {
        expectation.fulfill()
      }
    }
    let sut = OGDialogCoordinatorConnector()
    await sut.configure(dispatch: dispatch)
    await fulfillment(of: [expectation], timeout: 0.5)
    let actorActualEvents = await testActor.events
    XCTAssertTrue(actorActualEvents.contains(OGDialogCoordinatorAction._update(.stub)))
    XCTAssertTrue(actorActualEvents.contains(OGDialogCoordinatorAction._receivedScreenView(.stubs, .stub)))
  }

  func test_WHEN_webBridgeActionHandler_dispatch_receivedWebBridge() async throws {
    let expectation = expectation(description: "Expected set event to be received")
    OGDialogCoordinatorContainer
      .shared
      .webBridgeActionHandler()
      .webBridgeCallName
      .send(DialogCondition.stubWebBridgeCall.webBridgeCallName)
    let testActor = TestEventActor<OGDialogCoordinatorAction>()
    let dispatch = { event in
      await testActor.addEvent(event)
      if await testActor.events.count == 2 {
        expectation.fulfill()
      }
    }
    let sut = OGDialogCoordinatorConnector()
    await sut.configure(dispatch: dispatch)
    await fulfillment(of: [expectation], timeout: 0.5)
    let actorActualEvents = await testActor.events
    XCTAssertTrue(actorActualEvents.contains(OGDialogCoordinatorAction._update(.stub)))
    XCTAssertTrue(
      actorActualEvents.contains(
        OGDialogCoordinatorAction._receivedWebBridge(
          .stubs,
          DialogCondition.stubWebBridgeCall.webBridgeCallName!
        )
      )
    )
  }

  func test_WHEN_appStart_dispatch_appStartCount() async throws {
    let expectation = expectation(description: "Expected set event to be received")

    let testActor = TestEventActor<OGDialogCoordinatorAction>()
    let dispatch = { event in
      await testActor.addEvent(event)
      if await testActor.events.count == 2 {
        expectation.fulfill()
      }
    }

    let notificationCenter = NotificationCenterMock()
    notificationCenter.mock.didFinishLaunchingPublisherCalls.mockCall { _ in
      NotificationCenter.Publisher(center: .default, name: Notification.Name("didFinishLaunching"))
    }

    let sut = OGDialogCoordinatorConnector(notificationCenter: notificationCenter)
    await sut.configure(dispatch: dispatch)

    NotificationCenter.default.post(name: Notification.Name("didFinishLaunching"), object: "")

    await fulfillment(of: [expectation], timeout: 0.5)
    let actorActualEvents = await testActor.events
    XCTAssertTrue(actorActualEvents.contains(OGDialogCoordinatorAction._update(.stub)))
    XCTAssertTrue(actorActualEvents.contains(OGDialogCoordinatorAction._receivedAppStart(.stubs)))
    XCTAssertEqual(notificationCenter.mock.didFinishLaunchingPublisherCalls.callsCount, 1)
  }
}
