import Combine
import <PERSON>GCore
import OGCoreTestsUtils
import OGDialogCoordinatorTestsUtils
import OGFeatureAdapter
import OGMock
import OGScreenViewUpdate
import OGStorage
import OGSystemKit
import OGWebBridge
import OGWebBridgeTestsUtils
import XCTest

@testable import OGDialogCoordinator

final class OGDialogCoordinatorTests: XCTestCase {
  private var userNotificationCenterMock: UserNotificationCenterMock?
  private var webBridgeMock: WebBridgeMock?
  override func setUpWithError() throws {
    let webBridgeMock = WebBridgeMock()
    OGWebBridgeContainer.shared.globalWebBridge.register {
      webBridgeMock
    }

    OGCoreContainer.shared.storage.register {
      OGStorageMock()
    }

    OGDialogCoordinatorContainer.shared.webBridgeActionHandler.register {
      OGDialogCoordinatorWebBridgeActionHandlerMock()
    }
    userNotificationCenterMock = UserNotificationCenterMock()

    OGSystemKitContainer.shared.userNotificationCenter.register { [weak self] in
      self!.userNotificationCenterMock!
    }

    self.webBridgeMock = webBridgeMock

    try super.setUpWithError()
  }

  override func tearDownWithError() throws {
    OGWebBridgeContainer.shared.globalWebBridge.reset()
    OGCoreContainer.shared.storage.reset()
    OGDialogCoordinatorContainer.shared.webBridgeActionHandler.reset()
    userNotificationCenterMock = nil
    webBridgeMock = nil

    try super.tearDownWithError()
  }

  func test_WHEN_appStartCountFulfillment_THEN_behavior() async throws {
    userNotificationCenterMock?.mock.authorizationStatusCalls.mockCall { _ in
      UNAuthorizationStatus.notDetermined
    }

    let storageMock = OGStorageMock()
    let sut = OGDialogCoordinator(storage: storageMock)

    storageMock.mock.valueCalls.mockCall { _ in
      0
    }

    let behavior = await sut.hasAppStartCountFulfillment(for: .stubs)
    XCTAssertEqual(behavior, .stubPush)
  }

  func test_WHEN_OGDialogCoordinatorInit_THEN_createWebBridge() throws {
    userNotificationCenterMock?.mock.authorizationStatusCalls.mockCall { _ in
      UNAuthorizationStatus.notDetermined
    }
    let _ = OGDialogCoordinator()
    XCTAssertEqual(webBridgeMock?.mock.addActionHandlerCalls.callsCount, 1)
    XCTAssertEqual(webBridgeMock?.mock.addActionHandlerCalls.latestCall?.webBridgeNames, OGDialogCoordinatorContainer.shared.webBridgeActionHandler().webBridgeNames)
  }

  func test_WHEN_screenViewCountFulfillment_THEN_true() async throws {
    userNotificationCenterMock?.mock.authorizationStatusCalls.mockCall { _ in
      UNAuthorizationStatus.notDetermined
    }

    let storageMock = OGStorageMock()
    let sut = OGDialogCoordinator(storage: storageMock)

    storageMock.mock.valueCalls.mockCall { _ in
      2
    }

    _ = await sut.hasScreenViewCountFulfillment(for: .stubs, with: URL(string: "http://test")!)

    storageMock.mock.valueCalls.mockCall { _ in
      1
    }

    let behavior = await sut.hasScreenViewCountFulfillment(for: .stubs, with: URL(string: "http://test")!)
    XCTAssertEqual(behavior, .stubPush)
    XCTAssertEqual(userNotificationCenterMock?.mock.authorizationStatusCalls.callsCount, 1)
  }

  func test_WHEN_webBridgeCallNameFulfillment_THEN_behavior() async throws {
    userNotificationCenterMock?.mock.authorizationStatusCalls.mockCall { _ in
      UNAuthorizationStatus.notDetermined
    }

    let storageMock = OGStorageMock()
    let sut = OGDialogCoordinator(storage: storageMock)

    storageMock.mock.valueCalls.mockCall { _ in
      0
    }

    let behavior = await sut.hasWebBridgeCallNameFulfillment(for: .stubs, with: "webBridgeCallName")
    XCTAssertEqual(behavior, .stubPush)
    XCTAssertEqual(userNotificationCenterMock?.mock.authorizationStatusCalls.callsCount, 1)
  }

  func test_WHEN_webBridgeCallNameFulfillmentHasAuthorization_THEN_behavior() async throws {
    userNotificationCenterMock?.mock.authorizationStatusCalls.mockCall { _ in
      UNAuthorizationStatus.authorized
    }

    let storageMock = OGStorageMock()
    let sut = OGDialogCoordinator(storage: storageMock)

    storageMock.mock.valueCalls.mockCall { _ in
      0
    }

    let behavior = await sut.hasWebBridgeCallNameFulfillment(for: .stubs, with: "webBridgeCallName")
    XCTAssertEqual(behavior, .stubRating)
    XCTAssertEqual(userNotificationCenterMock?.mock.authorizationStatusCalls.callsCount, 1)
  }

  func test_WHEN_WebBridgeCallNameFulfillment_THEN_ratingBehaviorAfterPushBehavior() async throws {
    userNotificationCenterMock?.mock.authorizationStatusCalls.mockCall { _ in
      UNAuthorizationStatus.notDetermined
    }

    let storageMock = OGStorageMock()
    let sut = OGDialogCoordinator(storage: storageMock)

    storageMock.mock.valueCalls.mockCall { _ in
      0
    }

    let behaviorPush = await sut.hasWebBridgeCallNameFulfillment(for: .stubs, with: "webBridgeCallName")
    XCTAssertEqual(behaviorPush, .stubPush)

    let _ = sut.actionFor(behavior: behaviorPush!)

    storageMock.mock.valueCalls.mockCall { key in
      let ratingKey = OGDialogCoordinator.PersistedKey.invocationCountId(DialogBehavior.stubRating.id).rawValue
      return key.rawValue == ratingKey ? 0 : 2
    }

    let behaviorRating = await sut.hasWebBridgeCallNameFulfillment(for: .stubs, with: "webBridgeCallName")
    XCTAssertEqual(behaviorRating, .stubRating)
    XCTAssertEqual(userNotificationCenterMock?.mock.authorizationStatusCalls.callsCount, 1)
  }

  func test_WHEN_appStartCallNameFulfillmentHasAuthorization_THEN_behavior() async throws {
    userNotificationCenterMock?.mock.authorizationStatusCalls.mockCall { _ in
      UNAuthorizationStatus.authorized
    }

    let storageMock = OGStorageMock()
    let sut = OGDialogCoordinator(storage: storageMock)

    storageMock.mock.valueCalls.mockCall { _ in
      0
    }

    let stubBehavior = DialogBehavior.stubReviewFirstStart
    let behavior = await sut.hasAppStartCountFulfillment(for: [stubBehavior])
    XCTAssertEqual(behavior, .stubReviewFirstStart)
  }

  func test_WHEN_appStartCallNameFulfillmentHasAuthorization_THEN_noBehavior() async throws {
    userNotificationCenterMock?.mock.authorizationStatusCalls.mockCall { _ in
      UNAuthorizationStatus.authorized
    }

    let storageMock = OGStorageMock()
    let sut = OGDialogCoordinator(storage: storageMock)

    storageMock.mock.valueCalls.mockCall { _ in
      0
    }

    let stubBehavior = DialogBehavior.stubReviewSecondStart
    let behavior = await sut.hasAppStartCountFulfillment(for: [stubBehavior])
    XCTAssertNil(behavior)
  }

//  func test_WHEN_WebBridgeCallNameFulfillment_THEN_ratingBehaviorAfterPushBehavior() async throws {
//    userNotificationCenterMock?.mock.authorizationStatusCalls.mockCall { _ in
//      UNAuthorizationStatus.notDetermined
//    }
//    let sut = OGDialogCoordinator()
//    let behaviorPush = await sut.hasWebBridgeCallNameFulfillment(for: .stubs, with: "webBridgeCallName")
//    XCTAssertEqual(behaviorPush, .stubPush)
//    let _ = sut.actionFor(behavior: behaviorPush!)
//    let behaviorRating = await sut.hasWebBridgeCallNameFulfillment(for: .stubs, with: "webBridgeCallName")
//    XCTAssertEqual(behaviorRating, .stubReview)
//    XCTAssertEqual(userNotificationCenterMock?.mock.authorizationStatusCalls.callsCount, 1)
//  }

  func test_WHEN_actionForBehavior_THEN_action() async throws {
    userNotificationCenterMock?.mock.authorizationStatusCalls.mockCall { _ in
      UNAuthorizationStatus.notDetermined
    }

    let storageMock = OGStorageMock()
    let sut = OGDialogCoordinator(storage: storageMock)

    storageMock.mock.valueCalls.mockCall { _ in
      0
    }

    let behavior = DialogBehavior.stubPush
    let action = sut.actionFor(behavior: behavior)

    XCTAssertEqual(action, .stubPush)
    XCTAssertEqual(userNotificationCenterMock?.mock.authorizationStatusCalls.callsCount, 0)
  }
}
