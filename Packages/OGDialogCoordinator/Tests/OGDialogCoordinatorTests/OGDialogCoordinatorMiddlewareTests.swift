@testable import OGDialogCoordinator
import OGDialogCoordinatorTestsUtils
import OGDIService
import OGMock
import <PERSON>GR<PERSON><PERSON>
import OGRouterTestsUtils
import XCTest

final class OGDialogCoordinatorMiddlewareTests: XCTestCase {
  var dialogCoordinatorMock: OGDialogCoordinatorMock?
  var routePublisherMock: OGRoutePublisherMock?
  override func setUpWithError() throws {
    let dialogCoordinatorMock = OGDialogCoordinatorMock()
    OGDialogCoordinatorContainer.shared.dialogCoordinator.register {
      dialogCoordinatorMock
    }
    self.dialogCoordinatorMock = dialogCoordinatorMock

    let routePublisherMock = OGRoutePublisherMock()
    OGRoutingContainer.shared.routePublisher.register {
      routePublisherMock
    }
    self.routePublisherMock = routePublisherMock
    try super.setUpWithError()
  }

  override func tearDownWithError() throws {
    OGRoutingContainer.shared.routePublisher.reset()
    OGDialogCoordinatorContainer.shared.dialogCoordinator.reset()
    dialogCoordinatorMock = nil
    routePublisherMock = nil
    try super.tearDownWithError()
  }

  func test_GIVEN_initialState_WHEN_update_THEN_noNextAction() async throws {
    let sut = OGDialogCoordinatorMiddleware()
    let nextAction = try await sut.callAsFunction(
      action: ._update(.stub),
      for: .initial
    )
    XCTAssertNil(nextAction)
  }

  func test_GIVEN_state_WHEN_webBridge_THEN_triggeredBehavior() async throws {
    dialogCoordinatorMock?.mock.hasWebBridgeCallNameFulfillmentCalls.mockCall { _ in
      .stubPush
    }
    let sut = OGDialogCoordinatorMiddleware()
    let nextAction = try await sut.callAsFunction(
      action: ._receivedWebBridge(.stubs, DialogCondition.stubWebBridgeCall.webBridgeCallName!),
      for: .stub
    )
    XCTAssertEqual(dialogCoordinatorMock?.mock.hasWebBridgeCallNameFulfillmentCalls.callsCount, 1)
    XCTAssertEqual(dialogCoordinatorMock?.mock.hasWebBridgeCallNameFulfillmentCalls.latestCall?.0, .stubs)
    XCTAssertEqual(dialogCoordinatorMock?.mock.hasWebBridgeCallNameFulfillmentCalls.latestCall?.1, DialogCondition.stubWebBridgeCall.webBridgeCallName)
    XCTAssertEqual(nextAction, ._triggeredBehavior(.stubPush))
  }

  func test_GIVEN_state_WHEN_appStartCount_THEN_triggeredBehavior() async throws {
    let sut = OGDialogCoordinatorMiddleware()
    dialogCoordinatorMock?.mock.hasAppStartCountFulfillmentCalls.mockCall { _ in
      .stubPush
    }
    let nextAction = try await sut.callAsFunction(
      action: ._receivedAppStart(.stubs),
      for: .stub
    )
    XCTAssertEqual(dialogCoordinatorMock?.mock.hasAppStartCountFulfillmentCalls.callsCount, 1)
    XCTAssertEqual(dialogCoordinatorMock?.mock.hasAppStartCountFulfillmentCalls.latestCall, .stubs)
    XCTAssertEqual(nextAction, ._triggeredBehavior(.stubPush))
  }

  func test_GIVEN_state_WHEN_screenViewCount_THEN_triggeredBehavior() async throws {
    dialogCoordinatorMock?.mock.hasScreenViewCountFulfillmentCalls.mockCall { _ in
      .stubPush
    }

    let sut = OGDialogCoordinatorMiddleware()
    let nextAction = try await sut.callAsFunction(
      action: ._receivedScreenView(.stubs, .stub),
      for: .stub
    )
    XCTAssertEqual(dialogCoordinatorMock?.mock.hasScreenViewCountFulfillmentCalls.callsCount, 1)
    XCTAssertEqual(dialogCoordinatorMock?.mock.hasScreenViewCountFulfillmentCalls.latestCall?.0, .stubs)
    XCTAssertEqual(dialogCoordinatorMock?.mock.hasScreenViewCountFulfillmentCalls.latestCall?.1, .stub)
    XCTAssertEqual(nextAction, ._triggeredBehavior(.stubPush))
  }

  func test_GIVEN_state_WHEN_triggeredBehavior_THEN_navigate() async throws {
    dialogCoordinatorMock?.mock.actionForCalls.mockCall { _ in
      DialogBehavior.stubPush.action
    }
    let sut = OGDialogCoordinatorMiddleware()
    let nextAction = try await sut.callAsFunction(
      action: ._triggeredBehavior(.stubPush),
      for: .stub(willStartNavigateAt: Date.timeIntervalSinceReferenceDate)
    )
    XCTAssertEqual(dialogCoordinatorMock?.mock.actionForCalls.callsCount, 1)
    XCTAssertEqual(dialogCoordinatorMock?.mock.actionForCalls.latestCall, .stubPush)
    XCTAssertEqual(nextAction, ._navigate(DialogAction.stubPush.url))
  }

  func test_GIVEN_state_WHEN_triggeredBehaviorDebounced_THEN_noNextAction() async throws {
    dialogCoordinatorMock?.mock.actionForCalls.mockCall { _ in
      DialogBehavior.stubPush.action
    }
    let sut = OGDialogCoordinatorMiddleware()
    let nextAction = try await sut.callAsFunction(
      action: ._triggeredBehavior(.stubPush),
      for: .stub(willStartNavigateAt: Date.timeIntervalSinceReferenceDate, didStartNavigateAt: Date.timeIntervalSinceReferenceDate)
    )
    XCTAssertEqual(dialogCoordinatorMock?.mock.actionForCalls.callsCount, 0)
    XCTAssertEqual(dialogCoordinatorMock?.mock.actionForCalls.latestCall, nil)
    XCTAssertNil(nextAction)
  }

  func test_GIVEN_state_WHEN_navigate_THEN_noNextAction() async throws {
    let sut = OGDialogCoordinatorMiddleware()
    let nextAction = try await sut.callAsFunction(
      action: ._navigate(.stub),
      for: .stub
    )
    XCTAssertNil(nextAction)
    XCTAssertEqual(routePublisherMock?.mock.sendCalls.callsCount, 1)
    XCTAssertEqual(routePublisherMock?.mock.sendCalls.latestCall, OGRoute(url: .stub))
  }
}
