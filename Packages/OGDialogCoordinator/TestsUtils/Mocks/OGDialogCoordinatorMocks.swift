import Combine
import Foundation
import <PERSON>GCore
import OGCoreTestsUtils
import OGDialogCoordinator

import <PERSON>GMock
import <PERSON>GRouter
import OGWebBridge

// MARK: - OGDialogCoordinatorMock

@OGMock
public final class OGDialogCoordinatorMock: OGDialogCoordinable {
  public init() {}

  public func actionFor(behavior: DialogBehavior) -> DialogAction? {
    mock.actionFor(behavior: behavior)
  }

  public func createWebBridges(for behaviors: [DialogBehavior]) {
    mock.createWebBridges(for: behaviors)
  }

  public func hasWebBridgeCallNameFulfillment(for behaviors: [DialogBehavior], with webBridgeCallName: String) async -> DialogBehavior? {
    await mock.hasWebBridgeCallNameFulfillment(for: behaviors, with: webBridgeCallName)
  }

  public func hasScreenViewCountFulfillment(for behaviors: [DialogBehavior], with url: URL) -> DialogBehavior? {
    mock.hasScreenViewCountFulfillment(for: behaviors, with: url)
  }

  public func hasAppStartCountFulfillment(for behaviors: [DialogBehavior]) -> DialogBehavior? {
    mock.hasAppStartCountFulfillment(for: behaviors)
  }
}
