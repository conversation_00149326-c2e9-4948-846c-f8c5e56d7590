import Combine
import Foundation
import OGCore
import OGCoreTestsUtils
import OGMock
import OGScreenViewUpdate

// MARK: - ScreenViewUpdateMock

@OGMock
public final class ScreenViewUpdateMock: OGScreenViewUpdatable {
  public var update: CurrentValueSubject<URL?, Never> = CurrentValueSubject(nil)
  public init() {}
  public func send(with url: URL?) {
    mock.send(with: url)
    update.send(url)
  }
}
