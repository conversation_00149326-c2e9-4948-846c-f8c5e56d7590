import Combine
import OGCoreTestsUtils
import OGDialogCoordinator
import OGDomainStore
import OGDomainStoreTestsUtils

extension OGDialogCoordinatorStore where State == OGDialogCoordinatorState, Action == OGDialogCoordinatorAction {
  public static func makeMock(
    initialState: OGDialogCoordinatorState = OGDialogCoordinatorState(isAwaitingUpdate: false),
    reducerMock: OGDomainReducerMock<State, Action> = OGDomainReducerMock(),
    middlewareMock: OGDomainMiddlewareMock<State, Action> = OGDomainMiddlewareMock()
  )
    -> OGDialogCoordinatorStore {
    OGDialogCoordinatorStore(
      initialState: initialState,
      reducer: reducerMock.reduce,
      middlewares: middlewareMock
    )
  }
}
