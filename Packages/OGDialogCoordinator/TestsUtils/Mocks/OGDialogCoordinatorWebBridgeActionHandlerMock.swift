import Combine
import Foundation
import <PERSON>GCore
import OGCoreTestsUtils
import OGDialogCoordinator

import OGMock
import OGRouter
import OGWebBridge

// MARK: - OGDialogCoordinatorWebBridgeActionHandlerMock

@OGMock
public final class OGDialogCoordinatorWebBridgeActionHandlerMock: OGDialogCoordinatorWebBridgeActionHandlable {
  public var webBridgeCallName: CurrentValueSubject<String?, Never> = CurrentValueSubject(nil)

  public var webBridgeNames: [String] = []

  public init() {}

  public func update(webBridgeCallName: [String]) {
    mock.update(webBridgeCallName: webBridgeCallName)
  }

  public func canHandleUpdateAction(_ name: String) -> Bool {
    mock.canHandleUpdateAction(name)
  }

  public func handleUpdateAction(_ action: String, data: [String: Any]) {
    mock.handleUpdateAction(action, data: data)
  }
}
