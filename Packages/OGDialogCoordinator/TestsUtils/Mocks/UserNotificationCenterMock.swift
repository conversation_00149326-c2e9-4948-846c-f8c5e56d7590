import Foundation
import OGCore
import OGDialogCoordinator
import OGSystemKit
import UserNotifications

// MARK: - UserNotificationCenterMock

@OGMock
public final class UserNotificationCenterMock: OGUserNotificationCenterHandling {
  public init() {}

  public func authorizationStatus() async -> UNAuthorizationStatus {
    await mock.authorizationStatus()
  }

  public func requestAuthorization(options: UNAuthorizationOptions) async -> Bool {
    await mock.requestAuthorization(options: options)
  }
}
