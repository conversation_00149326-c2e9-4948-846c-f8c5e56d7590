import OGDIService
import OGIdentifier
import OGMacros
import OGTestEnvironmentKit
import XCTest

@testable import OGDetectiveComponent

class OGTestEnvironmentsModuleTests: XCTestCase {
  var environmentsServiceMock: OGTestEnvironmentsServiceableMock!
  var sut: OGTestEnvironmentsModule!

  override func setUp() {
    super.setUp()

    environmentsServiceMock = OGTestEnvironmentsServiceableMock()
    OGTestEnvironmentsContainer.shared.testEnvironmentsService.register {
      self.environmentsServiceMock
    }

    sut = OGTestEnvironmentsModule()
  }

  override func tearDown() {
    sut = nil
    environmentsServiceMock = nil
    OGTestEnvironmentsContainer.shared.reset()
    super.tearDown()
  }

  func testSelectedTestEnvironmentName_WhenNoEnvironmentSelected_ThenReturnsNoEnvironmentSelected() {
    // Given
    // When
    environmentsServiceMock.deselectTestEnvironment()
    let result = sut.selectedTestEnvironmentName

    // Then
    XCTAssertEqual(result, "Release Environment")
  }

  func testSelectedTestEnvironmentName_WhenEnvironmentSelected_ThenReturnsEnvironmentName() {
    // Given
    let identifier = #identifier("1234")
    let environment = OGTestEnvironment(identifier: identifier, isEnabled: true, custom: [:], features: [])

    // When
    environmentsServiceMock.testEnvironment = environment
    let result = sut.selectedTestEnvironmentName

    // Then
    XCTAssertEqual(result, identifier.value)
  }

  func testSelectTestEnvironment_ValidIdentifier_UpdatesSelectedEnvironmentInModule() throws {
    // Given
    let identifier1 = #identifier("1234")
    let identifier2 = #identifier("5678")

    let environment1 = OGTestEnvironment(identifier: identifier1, isEnabled: true, custom: [:], features: [])
    let environment2 = OGTestEnvironment(identifier: identifier2, isEnabled: true, custom: [:], features: [])
    environmentsServiceMock.environments.send([environment1, environment2])

    // When
    try environmentsServiceMock.selectTestEnvironment(for: identifier2)

    // Then
    XCTAssertEqual(sut.selectedTestEnvironmentName, identifier2.value)
  }

  func testSelectTestEnvironment_InvalidIdentifier_KeepsPreviousSelectedEnvironmentInModule() throws {
    // Given
    let identifier1 = #identifier("1234")
    let identifier2 = #identifier("9999")

    let environment1 = OGTestEnvironment(identifier: identifier1, isEnabled: true, custom: [:], features: [])
    environmentsServiceMock.environments.send([environment1])
    try environmentsServiceMock.selectTestEnvironment(for: identifier1)

    // When
    XCTAssertThrowsError(try environmentsServiceMock.selectTestEnvironment(for: identifier2)) { error in
      // Then
      XCTAssertEqual(sut.selectedTestEnvironmentName, identifier1.value)
      XCTAssertEqual((error as NSError).code, 404)
    }
  }

  func testDeselectTestEnvironment_AfterSelection_ResetsSelectedEnvironment() throws {
    // Given
    let identifier1 = #identifier("1234")
    let identifier2 = #identifier("5678")

    let environment1 = OGTestEnvironment(identifier: identifier1, isEnabled: true, custom: [:], features: [])
    let environment2 = OGTestEnvironment(identifier: identifier2, isEnabled: true, custom: [:], features: [])
    environmentsServiceMock.environments.send([environment1, environment2])

    // When
    try environmentsServiceMock.selectTestEnvironment(for: identifier2)
    XCTAssertEqual(sut.selectedTestEnvironmentName, identifier2.value)

    // Then
    environmentsServiceMock.deselectTestEnvironment()
    XCTAssertEqual(sut.selectedTestEnvironmentName, "Release Environment")
  }
}
