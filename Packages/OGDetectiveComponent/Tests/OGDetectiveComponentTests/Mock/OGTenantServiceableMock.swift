import Combine
import Foundation
import OGIdentifier
import OGTenantCore
import OGTenantKit

class OGTenantServiceableMock: OGTenantServiceable {
  @Published private var _selectedTenant: OGTenant? = nil

  public var selectedTenant: OGTenant? {
    get {
      _selectedTenant
    }
    set {
      _selectedTenant = newValue
    }
  }

  var selectedTenantPublished: Published<OGTenant?>.Publisher {
    $_selectedTenant
  }

  var tenants: [OGTenant] = []

  func setTenant(for identifier: OGIdentifier) throws {
    if let tenant = tenants.first(where: { $0.identifier == identifier }) {
      _selectedTenant = tenant
    } else {
      throw NSError(domain: "com.yourapp.error", code: 404, userInfo: [NSLocalizedDescriptionKey: "Tenant not found"])
    }
  }
}
