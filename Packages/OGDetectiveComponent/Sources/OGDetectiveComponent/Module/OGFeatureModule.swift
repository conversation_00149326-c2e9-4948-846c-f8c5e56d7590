import Combine
import OGDetective
import OGDIService
import OGFeatureConfigView
import OGFeatureCore
import OGFeatureManager
import SwiftUI

// MARK: - OGFeatureModuleProtocol

public protocol OGFeatureModuleProtocol: OGDetectiveModuleProtocol {}

// MARK: - OGFeatureModule

public final class OGFeatureModule: OGFeatureModuleProtocol {
  // MARK: - Properties

  public var identifier: UUID

  @OGInjected(\OGFeatureManagerContainer.featureMangerPublisher) private var featureMangerPublisher
  @OGInjected(\OGFeatureManagerContainer.featureManger) private var featureManager

  private var cancellables = Set<AnyCancellable>()
  private var snapshotData = [Date: [OGFeatureConfigView.OGFeatureModel]]()

  @Published private var allFeatures: [OGFeatureConfigView.OGFeatureModel]?

  public init() {
    self.identifier = UUID()
    featureMangerPublisher.featuresDidChangePublisher
      .receive(on: DispatchQueue.main)
      .compactMap { $0 }
      .map {
        Array($0)
          .sorted(by: { $0.identifier.value <= $1.identifier.value })
          .compactMap { self.mapFeatureIntoFeatureModel(feature: $0) }
      }
      .sink { features in
        self.allFeatures = features
      }
      .store(in: &cancellables)
  }

  // MARK: - Private Helpers

  private func mapFeatureIntoFeatureModel(feature: OGFeature) -> OGFeatureModel {
    let sortedCustomKeys = feature.custom.keys.sorted(by: <)
    return OGFeatureModel(
      id: feature.id,
      isEnabled: feature.isEnabled,
      custom: feature.custom,
      sortedCustomKeys: sortedCustomKeys,
      subFeatures: Set(feature.features.map { mapFeatureIntoFeatureModel(feature: $0) }.sorted(by: { $0.id < $1.id })),
      hasBeenChanged: featureManager.hasLocalFeatureChanges(id: feature.id),
      parentHasChanges: featureManager.hasOneOfLocalFeatureParentChanges(id: feature.id)
    )
  }

  private func pairs(for feature: OGFeatureModel) -> [(String, String)] {
    [
      ("ID", feature.id),
      ("Is Enabled", "\(feature.isEnabled)"),
      ("Has Been Changed", "\(feature.hasBeenChanged)"),
      ("Parent Has Changes", "\(feature.parentHasChanges)"),
      ("Custom", "\(feature.custom.description)"),
      ("Sorted Custom Keys", "\(feature.sortedCustomKeys.joined(separator: ", "))"),
      ("Sub Features Count", "\(feature.subFeatures.count)")
    ]
  }

  private func subtitle(for feature: OGFeatureModel) -> String {
    pairs(for: feature)
      .map { "\($0.0): \($0.1)" }
      .joined(separator: "\n")
  }

  // MARK: - Data Export

  public func exportData(forDate date: Date) -> String? {
    guard let features = snapshotData[date] else {
      return nil
    }

    return features.reduce("") { data, feature in
      var newData = data
      newData += "\(feature.id):\n"
      newData += subtitle(for: feature)
      newData += "\n-------\n"
      return newData
    }
  }

  public func didTakeSnapshot(atDate date: Date) {
    snapshotData[date] = allFeatures
  }

  // MARK: - Dashboard & Snapshot Views

  public func dashboardView() -> some View {
    OGDetectiveSectionCard(headline: "Configurations") {
      EmptyView()
    } detailView: {
      OGFeatureView()
    }
  }

  public func snapshotView(forSnapshotDate date: Date) -> some View {
    OGDetectiveSectionCard(headline: "Configurations") {
      EmptyView()
    } detailView: {
      OGFeatureSnapshotView(features: self.snapshotData[date] ?? [])
    }
  }
}
