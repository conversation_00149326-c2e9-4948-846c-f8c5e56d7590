import Combine
import OGCore
import OGDIService
import OGFeatureCore
import OGTestEnvironmentKit

final class EnvironmentViewModel: ObservableObject {
  var preConfiguredEnvironments: [OGTestEnvironment]
  @Published var selectedEnvironment: OGTestEnvironment?
  @Published var previousSelectedEnvironment: OGTestEnvironment?
  @Published var selectedCustomEnvironment: OGTestEnvironment?
  @Published var isAddingNewEnvironment: Bool = false
  @Published var isEditingOrDeletingEnvironment: Bool = false
  @Published var isUpdatingEnvironment: Bool = false
  @Published var isAlertPresented: Bool = false
  @Published var feedbackType: FeedbackType = .none

  var selectedIdentifier: String
  private var addTestEnvironment: ((String, AnyJSONType, String) throws -> Void)?
  private var updateCustomEnvironment: ((String, AnyJSONType, String, OGIdentifier) throws -> Void)?
  private var removeCustomEnvironment: ((OGIdentifier) throws -> Void)?

  var onSelect: (OGIdentifier) -> Void
  var onDeselect: () -> Void

  @OGInjected(\OGCoreContainer.logger) var logger

  init(
    preConfiguredEnvironments: [OGTestEnvironment],
    selectedIdentifier: String,
    addTestEnvironment: ((String, AnyJSONType, String) throws -> Void)?,
    updateCustomEnvironment: ((String, AnyJSONType, String, OGIdentifier) throws -> Void)? = nil,
    removeCustomEnvironment: ((OGIdentifier) throws -> Void)? = nil,
    onSelect: @escaping (OGIdentifier) -> Void,
    onDeselect: @escaping () -> Void
  ) {
    self.preConfiguredEnvironments = preConfiguredEnvironments
    self.selectedIdentifier = selectedIdentifier
    self.addTestEnvironment = addTestEnvironment
    self.updateCustomEnvironment = updateCustomEnvironment
    self.removeCustomEnvironment = removeCustomEnvironment
    self.onSelect = onSelect
    self.onDeselect = onDeselect

    self.selectedEnvironment = preConfiguredEnvironments.first(where: { $0.identifier.value == selectedIdentifier })
  }

  func addEnvironment(url: String, json: AnyJSONType, password: String) {
    do {
      try addTestEnvironment?(url, json, password)
      feedbackType = .added
    } catch {
      logger.log(.debug, domain: .state, message: "Error adding new environment: \(error)")
    }
  }

  func updateEnvironment(name: String, json: AnyJSONType, password: String, identifier: OGIdentifier) {
    do {
      try updateCustomEnvironment?(name, json, password, identifier)
      feedbackType = .updated
    } catch {
      logger.log(.debug, domain: .state, message: "Error updating selected environment: \(error)")
    }
  }

  func removeEnvironment(identifier: OGIdentifier) {
    do {
      try removeCustomEnvironment?(identifier)
      feedbackType = .removed
    } catch {
      logger.log(.debug, domain: .state, message: "Error removing selected environment: \(error)")
    }
  }

  func editOrDeleteEnvironment() {
    isEditingOrDeletingEnvironment = true
  }

  func selectReleaseEnvironment() {
    previousSelectedEnvironment = selectedEnvironment
    selectedEnvironment = nil
    isAlertPresented = true
  }

  func selectEnvironment(_ environment: OGTestEnvironment) {
    previousSelectedEnvironment = selectedEnvironment
    selectedEnvironment = environment
    isAlertPresented = true
  }

  func confirmSelection() {
    if let identifier = selectedEnvironment?.identifier {
      onSelect(identifier)
    } else {
      onDeselect()
    }
  }

  func cancelSelection() {
    selectedEnvironment = previousSelectedEnvironment
  }

  func addNewEnvironment() {
    isAddingNewEnvironment = true
  }

  func url(of environment: OGTestEnvironment) -> String {
    do {
      let feature = try environment.feature(for: #identifier("baseUrl"))
      return feature.customValue(for: "web") ?? ""
    } catch {
      return ""
    }
  }
}
