import OGDetective
import SwiftUI

// MARK: - CloseButtonModifier

struct CloseButtonModifier: ViewModifier {
  var action: () -> Void

  func body(content: Content) -> some View {
    ZStack(alignment: .topTrailing) {
      content
      Button(action: action) {
        Image(systemName: "xmark.circle.fill")
          .titelMBold()
          .foregroundStyle(Theme.Token.Colors.background_80, Theme.Token.Colors.background_20)
      }
      .padding()
    }
  }
}
