import OGDetective
import OGTenantCore
import SwiftUI

struct TenantSectionView: View {
  var tenant: OGTenant?
  var url: String

  var body: some View {
    VStack(alignment: .leading) {
      Text("Tenant")
        .captionLRegular()
        .foregroundColor(Theme.Token.Colors.background_60)
      HStack(alignment: .top) {
        if let regionCode = tenant?.locale.regionCode {
          FlagView(countryFlagCode: regionCode)
        }
        VStack(alignment: .leading, spacing: 0) {
          Text(tenant?.displayName ?? "")
            .titelSBold()
            .foregroundColor(Theme.Token.Colors.background_100)
          Text(url)
            .bodyRegular()
            .foregroundColor(Theme.Token.Colors.background_60)
        }
      }
    }
  }
}
