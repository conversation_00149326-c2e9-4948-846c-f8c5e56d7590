import OGDetective
import OGTenantCore
import SwiftUI

// MARK: - TenantRow

struct TenantRowView: View {
  let tenant: OGTenant
  let isSelected: Bool
  let onInfoTap: () -> Void

  var body: some View {
    HStack(spacing: 0) {
      HStack(spacing: 0) {
        if let regionCode = tenant.locale.regionCode {
          FlagView(countryFlagCode: regionCode)
            .padding(.trailing)
        }
        Text(tenant.displayName)
          .foregroundColor(Theme.Token.Colors.background_100)
        Spacer()
      }
      .padding(.vertical)
      .contentShape(Rectangle())
      Button(action: onInfoTap) {
        Image(systemName: "info.circle")
          .titelSBold()
      }
      .padding(.trailing)
    }
  }
}
