import Combine
import OGDIService
import OGDomainStore
import OGNavigationCore
import OGUserCore

public typealias OGBadgeStore = OGDomainStore<OGBadgeState, OGBadgeAction>

extension OGDomainStore where State == OGBadgeState, Action == OGBadgeAction {
  public static func make() -> OGBadgeStore {
    OGBadgeStore(
      reducer: OGBadgeState.Reducer.reduce,
      middlewares: OGBadgeState.Middleware(),
      connector: OGBadgeState.Connector()
    )
  }
}

// MARK: - OGBadgeState

public struct OGBadgeState: OGDomainState, Equatable {
  public private(set) var isAwaitingUpdate: Bool
  public private(set) var badgeContent: String
  public private(set) var tab: OGTab
  public private(set) var isLoggedIn: Bool
  public private(set) var isEnabled: Bool

  public init(
    isAwaitingUpdate: Bool = false,
    badgeContent: String,
    tab: OGTab,
    isLoggedIn: Bool,
    isEnabled: Bool = true
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate
    self.badgeContent = badgeContent
    self.tab = tab
    self.isLoggedIn = isLoggedIn
    self.isEnabled = isEnabled
  }

  private mutating func update(
    tab: OGTab? = nil,
    badgeContent: String? = nil,
    isLoggedIn: Bool? = nil,
    isEnabled: Bool? = nil
  ) {
    var tab = tab ?? self.tab
    let isLoggedIn = isLoggedIn ?? self.isLoggedIn
    let badgeContent = badgeContent ?? self.badgeContent
    let isEnabled = isEnabled ?? self.isEnabled
    if isEnabled {
      isLoggedIn ? tab.update(badge: badgeContent) : tab.update(badge: nil)
    } else {
      tab.update(badge: nil)
    }

    self.isEnabled = isEnabled
    self.tab = tab
    self.badgeContent = badgeContent
    self.isLoggedIn = isLoggedIn
  }

  public static let initial: Self = .init(badgeContent: "", tab: .none, isLoggedIn: false)
}

// MARK: - OGBadgeAction

public enum OGBadgeAction: OGDomainAction, Equatable {
  case setTab(tab: OGTab, badgeContent: String)
  case showTabBarBadge(Bool)

  /// Private
  case _updateUserIsLoggedIn(Bool)
  case _updateLoginBadge
}

// MARK: - Reducer & Connector

extension OGBadgeState {
  public enum Reducer: OGDomainActionReducible {
    public static func reduce(
      _ state: inout OGBadgeState,
      with action: OGBadgeAction
    ) {
      switch action {
      case let ._updateUserIsLoggedIn(isLoggedIn):
        state.update(isLoggedIn: isLoggedIn)
      case let .showTabBarBadge(isEnabled):
        state.update(isEnabled: isEnabled)
      case ._updateLoginBadge:
        break
      case let .setTab(tab, badgeContent):
        state.update(tab: tab, badgeContent: badgeContent)
      }
    }
  }

  public struct Middleware: OGDomainMiddleware {
    private var badgePublisher: OGBadgePublishing

    init(badgePublisher: OGBadgePublishing = OGBadgeContainer.shared.badgePublisher()) {
      self.badgePublisher = badgePublisher
    }

    public func callAsFunction(
      action: OGBadgeAction,
      for state: OGBadgeState
    ) async
      -> OGBadgeAction? {
      switch action {
      case ._updateUserIsLoggedIn:
        return ._updateLoginBadge
      case ._updateLoginBadge, .setTab, .showTabBarBadge:
        badgePublisher.send(state.tab)
        return nil
      }
    }
  }

  public actor Connector: OGDomainConnector {
    private let userStore: any OGDomainStoreViewStoreConsumable<UserState>

    private var cancellables = Set<AnyCancellable>()

    public init(
      userStore: any OGDomainStoreViewStoreConsumable<UserState> = OGUserStoreContainer.shared.userStore()
    ) {
      self.userStore = userStore
    }

    public func configure(dispatch: @escaping (OGBadgeAction) async -> Void) async {
      await userStore
        .watch(keyPath: \.isLoggedIn)
        .removeDuplicates()
        .sink { userState in
          Task {
            await dispatch(._updateUserIsLoggedIn(userState))
          }
        }
        .store(in: &cancellables)
    }
  }
}
