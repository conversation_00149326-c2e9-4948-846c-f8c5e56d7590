import Combine
import OGNavigationCore

// MARK: - OGBadgePublishing

/// A protocol that defines the configuration options for an OGTab.
public protocol OGBadgePublishing {
  /// Sends the updated tab with badge information.
  /// - Parameter tab: The tab with updated badge information.
  func send(_ tab: OGTab)
  /// The provider for badge updates.
  var provider: PassthroughSubject<OGTab, Never> { get }
}

// MARK: - OGBadgePublisher

/// Representing an OGBadgePublisher that conforms to the OGBadgePublishing protocol.
public final class OGBadgePublisher: OGBadgePublishing {
  /// The provider for badge updates.
  public private(set) var provider: PassthroughSubject<OGTab, Never> = PassthroughSubject()
  /// Sends the updated tab with badge information.
  /// - Parameter tab: The tab with updated badge information.
  public func send(_ tab: OGTab) {
    provider.send(tab)
  }
}
