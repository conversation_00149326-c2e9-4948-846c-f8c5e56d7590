import Combine
import OGNavigationCore
import XCTest

@testable import OGBadge

final class OGBadgePublisherTests: XCTestCase {
  private var cancellables: Set<AnyCancellable> = []

  override func setUpWithError() throws {
    try super.setUpWithError()
    cancellables = []
  }

  override func tearDownWithError() throws {
    cancellables.forEach { $0.cancel() }
    cancellables = []
    try super.tearDownWithError()
  }

  func testSendTabUpdatesProvider() throws {
    let badgePublisher = OGBadgePublisher()
    let tabName = "TestTabName"
    let expectedTab = OGTab(name: tabName, rawValue: tabName)
    let expectation = XCTestExpectation(description: "Provider sends updated tab")

    var receivedTab: OGTab?
    badgePublisher.provider
      .sink(receiveValue: { tab in
        receivedTab = tab
        expectation.fulfill()
      })
      .store(in: &cancellables)

    badgePublisher.send(expectedTab)

    wait(for: [expectation], timeout: 0.1)

    XCTAssertEqual(receivedTab, expectedTab)
    XCTAssertEqual(receivedTab?.name, expectedTab.name)
  }
}
