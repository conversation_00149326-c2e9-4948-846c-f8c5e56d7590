import Combine
import OGBadgeTestsUtils
import <PERSON>GCoreTestsUtils
import OGNavigationCore
import OG<PERSON>serCore
import OGUserCoreTestsUtils
import XCTest

@testable import OGBadge

// MARK: - OGBadgeStoreConnectorTests

final class OGBadgeStoreConnectorTests: XCTestCase {
  override func setUpWithError() throws {
    try super.setUpWithError()
  }

  override func tearDownWithError() throws {
    OGUserStoreContainer.shared.reset()
    try super.tearDownWithError()
  }

  func test_WHEN_updateUserIsLoggedIn_THEN_updateAccountBadge() async throws {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Expected set url dispatched")

      OGUserStoreContainer.shared.userStore.register {
        UserStore.makeMock(initialState: .loggedInStub)
      }

      let testActor = TestEventActor<OGBadgeAction>()

      let dispatch = { event in
        await testActor.addEvent(event)
        if await testActor.events.count == 1 {
          expectation.fulfill()
        }
      }

      let sut = OGBadgeState.Connector()
      await sut.configure(dispatch: dispatch)
      await fulfillment(of: [expectation], timeout: 0.2)

      let expected: [OGBadgeAction] = [
        ._updateUserIsLoggedIn(true)
      ]
      let actual = await testActor.events
      XCTAssertEqual(actual, expected)
    }
  }
}
